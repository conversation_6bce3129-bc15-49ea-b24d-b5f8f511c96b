-- =====================================================
-- 工作流节点定义示例数据
-- 展示4条产品线的差异化流程配置
-- =====================================================

-- 产品线A的节点定义
-- =====================================================

-- 主节点1：销售创建项目
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`) VALUES
(1, 'A_MAIN_1', '销售创建项目', 'main', 1, 'sales', '["create_project", "input_customer_info", "select_product_line"]', 0, 1);

-- 主节点2：主部门负责人选择执行人员
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`) VALUES
(1, 'A_MAIN_2', '选择主部门执行人员', 'main', 2, 'main_manager', '["select_executor"]', 0, 1);

-- 主节点3：主部门执行人员上传方案
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`) VALUES
(1, 'A_MAIN_3', '上传方案或资料', 'main', 3, 'main_executor', '["upload_proposal", "input_description"]', 0, 0);

-- 主节点4：主部门负责人审批方案
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `reject_to_node`, `auto_assign`) VALUES
(1, 'A_MAIN_4', '审批方案或资料', 'main', 4, 'main_manager', '["review_proposal", "approve_or_reject"]', 1, 'A_MAIN_3', 0);

-- 主节点5：销售更新产品信息
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`) VALUES
(1, 'A_MAIN_5', '更新产品信息', 'main', 5, 'sales', '["update_product_info", "update_signing_status"]', 0, 0);

-- 主节点6：商务审核未中标（分支节点）
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `conditions`) VALUES
(1, 'A_MAIN_6_LOST', '审核项目未中标', 'main', 6, 'business', '["review_lost_project", "confirm_end"]', 0, 1, '{"signing_status": "lost"}');

-- 主节点6：选择协同部门（中标分支）
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `conditions`) VALUES
(1, 'A_MAIN_6_WON', '选择协同部门', 'main', 6, 'main_manager', '["select_collaborative_departments", "confirm_cooperation_content"]', 0, 0, '{"signing_status": "won"}');

-- 协同节点1：协同部门负责人选择执行人员
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `is_parallel`) VALUES
(1, 'A_COLLAB_1', '选择项目执行人员', 'collaborative', 7, 'collab_manager', '["select_executor", "update_data_content"]', 0, 0, 1);

-- 协同节点2：协同部门执行人员选择数据分析师
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `is_parallel`) VALUES
(1, 'A_COLLAB_2', '选择数据分析师', 'collaborative', 8, 'collab_executor', '["select_analyst", "update_data_content"]', 0, 0, 1);

-- 协同节点3：数据分析师上传原始数据
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `is_parallel`) VALUES
(1, 'A_COLLAB_3', '上传原始数据', 'collaborative', 9, 'collab_analyst', '["upload_raw_data"]', 0, 0, 1);

-- 协同节点4：协同部门执行人员审核数据
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `reject_to_node`, `auto_assign`, `is_parallel`) VALUES
(1, 'A_COLLAB_4', '审核原始数据', 'collaborative', 10, 'collab_executor', '["review_raw_data", "approve_or_reject"]', 1, 'A_COLLAB_3', 0, 1);

-- 主节点7：主部门执行人员确认数据并上传PPT
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `reject_to_node`, `auto_assign`) VALUES
(1, 'A_MAIN_7', '确认数据并上传PPT', 'main', 11, 'main_executor', '["confirm_or_reject_data", "upload_ppt", "support_trace_reject"]', 1, 'A_COLLAB_3', 0);

-- 主节点8：主部门负责人审批PPT并交付
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `reject_to_node`, `auto_assign`) VALUES
(1, 'A_MAIN_8', '审批PPT并跟进客户', 'main', 12, 'main_manager', '["review_ppt", "follow_customer_or_delivery"]', 1, 'A_MAIN_7', 0);

-- 产品线B的节点定义（简化流程）
-- =====================================================

-- 主节点1-5：与产品线A相同
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`) VALUES
(2, 'B_MAIN_1', '销售创建项目', 'main', 1, 'sales', '["create_project", "input_customer_info", "select_product_line"]', 0, 1),
(2, 'B_MAIN_2', '选择主部门执行人员', 'main', 2, 'main_manager', '["select_executor"]', 0, 1),
(2, 'B_MAIN_3', '上传方案或资料', 'main', 3, 'main_executor', '["upload_proposal", "input_description"]', 0, 0),
(2, 'B_MAIN_4', '审批方案或资料', 'main', 4, 'main_manager', '["review_proposal", "approve_or_reject"]', 1, 'B_MAIN_3', 0),
(2, 'B_MAIN_5', '更新产品信息', 'main', 5, 'sales', '["update_product_info", "update_signing_status"]', 0, 0);

-- 主节点6：商务审核未中标
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `conditions`) VALUES
(2, 'B_MAIN_6_LOST', '审核项目未中标', 'main', 6, 'business', '["review_lost_project", "confirm_end"]', 0, 1, '{"signing_status": "lost"}');

-- 主节点6：选择主部门执行人员（中标分支，B线无协同部门）
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `conditions`) VALUES
(2, 'B_MAIN_6_WON', '选择主部门执行人员', 'main', 6, 'main_manager', '["select_executor"]', 0, 0, '{"signing_status": "won"}');

-- 主节点7：一键完成或上传文件并交付
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`) VALUES
(2, 'B_MAIN_7', '一键完成并交付', 'main', 7, 'main_executor', '["one_click_complete_or_upload", "delivery"]', 0, 0);

-- 产品线C的节点定义（中等复杂度协同流程）
-- =====================================================

-- 主节点1-6：与产品线A类似
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`) VALUES
(3, 'C_MAIN_1', '销售创建项目', 'main', 1, 'sales', '["create_project", "input_customer_info", "select_product_line"]', 0, 1),
(3, 'C_MAIN_2', '选择主部门执行人员', 'main', 2, 'main_manager', '["select_executor"]', 0, 1),
(3, 'C_MAIN_3', '上传方案或资料', 'main', 3, 'main_executor', '["upload_proposal", "input_description"]', 0, 0),
(3, 'C_MAIN_4', '审批方案或资料', 'main', 4, 'main_manager', '["review_proposal", "approve_or_reject"]', 1, 'C_MAIN_3', 0),
(3, 'C_MAIN_5', '更新产品信息', 'main', 5, 'sales', '["update_product_info", "update_signing_status"]', 0, 0);

INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `conditions`) VALUES
(3, 'C_MAIN_6_LOST', '审核项目未中标', 'main', 6, 'business', '["review_lost_project", "confirm_end"]', 0, 1, '{"signing_status": "lost"}'),
(3, 'C_MAIN_6_WON', '选择协同部门', 'main', 6, 'main_manager', '["select_collaborative_departments", "confirm_cooperation_content"]', 0, 0, '{"signing_status": "won"}');

-- 协同节点1：协同部门负责人选择执行人员
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `is_parallel`) VALUES
(3, 'C_COLLAB_1', '选择项目执行人员', 'collaborative', 7, 'collab_manager', '["select_executor"]', 0, 0, 1);

-- 协同节点2：协同部门执行人员上传数据（无数据分析师）
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `is_parallel`) VALUES
(3, 'C_COLLAB_2', '上传原始数据', 'collaborative', 8, 'collab_executor', '["upload_raw_data"]', 0, 0, 1);

-- 主节点7：确认数据并上传PPT
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `reject_to_node`, `auto_assign`) VALUES
(3, 'C_MAIN_7', '确认数据并上传PPT', 'main', 9, 'main_executor', '["confirm_or_reject_data", "upload_ppt", "support_trace_reject"]', 1, 'C_COLLAB_2', 0);

-- 主节点8：审批PPT并交付
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `reject_to_node`, `auto_assign`) VALUES
(3, 'C_MAIN_8', '审批PPT并交付', 'main', 10, 'main_manager', '["review_ppt", "delivery"]', 1, 'C_MAIN_7', 0);

-- 产品线D的节点定义（需要手动选择主部门）
-- =====================================================

-- 主节点1：销售创建项目（D线需要手动选择主部门）
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`) VALUES
(4, 'D_MAIN_1', '销售创建项目', 'main', 1, 'sales', '["create_project", "input_customer_info", "select_product_line", "manual_select_main_department"]', 0, 1);

-- 其余节点与产品线A类似，但协同流程稍有不同
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`) VALUES
(4, 'D_MAIN_2', '选择主部门执行人员', 'main', 2, 'main_manager', '["select_executor"]', 0, 1),
(4, 'D_MAIN_3', '上传方案或资料', 'main', 3, 'main_executor', '["upload_proposal", "input_description"]', 0, 0),
(4, 'D_MAIN_4', '审批方案或资料', 'main', 4, 'main_manager', '["review_proposal", "approve_or_reject"]', 1, 'D_MAIN_3', 0),
(4, 'D_MAIN_5', '更新产品信息', 'main', 5, 'sales', '["update_product_info", "update_signing_status"]', 0, 0);

INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `conditions`) VALUES
(4, 'D_MAIN_6_LOST', '审核项目未中标', 'main', 6, 'business', '["review_lost_project", "confirm_end"]', 0, 1, '{"signing_status": "lost"}'),
(4, 'D_MAIN_6_WON', '选择协同部门', 'main', 6, 'main_manager', '["select_collaborative_departments", "confirm_cooperation_content"]', 0, 0, '{"signing_status": "won"}');

-- D线协同流程：协同部门负责人选择执行人员
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `is_parallel`) VALUES
(4, 'D_COLLAB_1', '选择项目执行人员', 'collaborative', 7, 'collab_manager', '["select_executor"]', 0, 0, 1);

-- D线协同流程：协同部门执行人员上传数据
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`, `is_parallel`) VALUES
(4, 'D_COLLAB_2', '上传原始数据', 'collaborative', 8, 'collab_executor', '["upload_raw_data"]', 0, 0, 1);

-- D线协同流程：协同部门负责人审核数据
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `reject_to_node`, `auto_assign`, `is_parallel`) VALUES
(4, 'D_COLLAB_3', '审核原始数据', 'collaborative', 9, 'collab_manager', '["review_raw_data", "approve_or_reject"]', 1, 'D_COLLAB_2', 0, 1);

-- D线主节点7：数据质检并上传PPT
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `auto_assign`) VALUES
(4, 'D_MAIN_7', '数据质检并上传PPT', 'main', 10, 'main_executor', '["data_quality_check", "upload_ppt"]', 0, 0);

-- D线主节点8：审批PPT（可跳过）并跟进客户
INSERT INTO `node_definitions` (`workflow_id`, `node_code`, `node_name`, `node_type`, `sequence`, `assignee_type`, `action_required`, `can_reject`, `reject_to_node`, `auto_assign`) VALUES
(4, 'D_MAIN_8', '审批PPT并跟进客户', 'main', 11, 'main_manager', '["review_ppt_optional", "follow_customer_or_delivery"]', 1, 'D_MAIN_7', 0);
