<?php

/**
 * 工作流系统测试脚本
 * 用于验证系统的基本功能是否正常
 */

require_once 'vendor/autoload.php';

// 设置环境
putenv('CI_ENVIRONMENT=development');

// 初始化CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

echo "=== 工作流系统测试 ===\n\n";

try {
    // 测试数据库连接
    echo "1. 测试数据库连接...\n";
    $db = \Config\Database::connect();
    $query = $db->query("SELECT 1 as test");
    $result = $query->getRow();
    if ($result && $result->test == 1) {
        echo "✓ 数据库连接成功\n\n";
    } else {
        echo "✗ 数据库连接失败\n\n";
        exit(1);
    }

    // 测试模型
    echo "2. 测试模型...\n";
    
    // 测试工作流定义模型
    $workflowModel = new \App\Models\WorkflowDefinitionModel();
    $workflows = $workflowModel->getAllActive();
    echo "✓ 工作流定义模型正常，当前有 " . count($workflows) . " 个激活的工作流\n";
    
    // 测试项目模型
    $projectModel = new \App\Models\ProjectModel();
    $stats = $projectModel->getStatistics();
    echo "✓ 项目模型正常，总项目数: " . $stats['total'] . "\n";
    
    // 测试部门模型
    $departmentModel = new \App\Models\DepartmentModel();
    $mainDepts = $departmentModel->getMainDepartments();
    echo "✓ 部门模型正常，主部门数: " . count($mainDepts) . "\n";
    
    // 测试用户模型
    $userModel = new \App\Models\UserModel();
    $users = $userModel->findAll();
    echo "✓ 用户模型正常，用户数: " . count($users) . "\n\n";

    // 测试工作流引擎
    echo "3. 测试工作流引擎...\n";
    $workflowEngine = new \App\Services\WorkflowEngine();
    
    // 如果有项目，测试获取工作流状态
    if ($stats['total'] > 0) {
        $projects = $projectModel->limit(1)->findAll();
        if (!empty($projects)) {
            $project = $projects[0];
            $status = $workflowEngine->getWorkflowStatus($project['id']);
            echo "✓ 工作流引擎正常，项目 {$project['id']} 状态获取成功\n";
        }
    } else {
        echo "✓ 工作流引擎初始化正常（暂无项目数据）\n";
    }
    
    echo "\n";

    // 测试路由配置
    echo "4. 测试路由配置...\n";
    $routes = \Config\Services::routes();
    echo "✓ 路由配置加载成功\n\n";

    // 显示系统信息
    echo "5. 系统信息:\n";
    echo "- CodeIgniter版本: " . \CodeIgniter\CodeIgniter::CI_VERSION . "\n";
    echo "- PHP版本: " . PHP_VERSION . "\n";
    echo "- 环境: " . ENVIRONMENT . "\n";
    echo "- 数据库: " . $db->getDatabase() . "\n";
    echo "- 时区: " . date_default_timezone_get() . "\n";
    echo "- 当前时间: " . date('Y-m-d H:i:s') . "\n\n";

    echo "=== 所有测试通过！系统运行正常 ===\n";

} catch (\Exception $e) {
    echo "✗ 测试失败: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    exit(1);
}

/**
 * 创建示例数据的函数
 */
function createSampleData() {
    echo "\n=== 创建示例数据 ===\n";
    
    try {
        // 创建示例部门
        $departmentModel = new \App\Models\DepartmentModel();
        
        $departments = [
            [
                'name' => '产品A部门',
                'code' => 'PRODUCT_A',
                'type' => 'main',
                'product_lines' => json_encode(['A']),
                'status' => 1,
                'description' => '负责产品线A的主部门'
            ],
            [
                'name' => '产品B部门',
                'code' => 'PRODUCT_B', 
                'type' => 'main',
                'product_lines' => json_encode(['B']),
                'status' => 1,
                'description' => '负责产品线B的主部门'
            ],
            [
                'name' => '技术支持部',
                'code' => 'TECH_SUPPORT',
                'type' => 'collaborative',
                'product_lines' => json_encode(['A', 'B', 'C', 'D']),
                'status' => 1,
                'description' => '技术支持协同部门'
            ],
            [
                'name' => '数据分析部',
                'code' => 'DATA_ANALYSIS',
                'type' => 'collaborative',
                'product_lines' => json_encode(['A', 'C']),
                'status' => 1,
                'description' => '数据分析协同部门'
            ]
        ];
        
        foreach ($departments as $dept) {
            $existing = $departmentModel->where('code', $dept['code'])->first();
            if (!$existing) {
                $departmentModel->createDepartment($dept);
                echo "✓ 创建部门: {$dept['name']}\n";
            }
        }
        
        // 创建示例用户
        $userModel = new \App\Models\UserModel();
        
        $users = [
            [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => '123456',
                'real_name' => '系统管理员',
                'phone' => '13800138000',
                'status' => 1
            ],
            [
                'username' => 'sales001',
                'email' => '<EMAIL>',
                'password' => '123456',
                'real_name' => '销售员001',
                'phone' => '13800138001',
                'status' => 1
            ],
            [
                'username' => 'manager001',
                'email' => '<EMAIL>',
                'password' => '123456',
                'real_name' => '部门经理001',
                'phone' => '13800138002',
                'status' => 1
            ]
        ];
        
        foreach ($users as $user) {
            $existing = $userModel->where('username', $user['username'])->first();
            if (!$existing) {
                $userModel->createUser($user);
                echo "✓ 创建用户: {$user['real_name']}\n";
            }
        }
        
        // 创建示例工作流定义
        $workflowModel = new \App\Models\WorkflowDefinitionModel();
        
        $workflows = [
            [
                'name' => '产品线A标准流程',
                'code' => 'WORKFLOW_A',
                'product_line' => 'A',
                'version' => '1.0',
                'is_active' => 1,
                'description' => '产品线A的标准工作流程'
            ],
            [
                'name' => '产品线B简化流程',
                'code' => 'WORKFLOW_B',
                'product_line' => 'B',
                'version' => '1.0',
                'is_active' => 1,
                'description' => '产品线B的简化工作流程'
            ]
        ];
        
        foreach ($workflows as $workflow) {
            $existing = $workflowModel->where('code', $workflow['code'])->first();
            if (!$existing) {
                $workflowModel->insert($workflow);
                echo "✓ 创建工作流: {$workflow['name']}\n";
            }
        }
        
        echo "=== 示例数据创建完成 ===\n";
        
    } catch (\Exception $e) {
        echo "✗ 创建示例数据失败: " . $e->getMessage() . "\n";
    }
}

// 如果传入参数 --create-sample，则创建示例数据
if (isset($argv[1]) && $argv[1] === '--create-sample') {
    createSampleData();
}
