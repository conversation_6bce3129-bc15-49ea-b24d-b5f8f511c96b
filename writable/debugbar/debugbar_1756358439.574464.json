{"url": "http://adm.ws.loc/index.php/manage/nodes/create", "method": "GET", "isAJAX": false, "startTime": **********.478957, "totalTime": 66.5, "totalMemory": "6.346", "segmentDuration": 10, "segmentCount": 7, "CI_VERSION": "4.6.3", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.482445, "duration": 0.023968935012817383}, {"name": "Required Before Filters", "component": "Timer", "start": **********.506416, "duration": 0.003434896469116211}, {"name": "Routing", "component": "Timer", "start": **********.509857, "duration": 0.0011060237884521484}, {"name": "Before Filters", "component": "Timer", "start": **********.511241, "duration": 4.506111145019531e-05}, {"name": "Controller", "component": "Timer", "start": **********.511288, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.511289, "duration": 0.010718107223510742}, {"name": "After Filters", "component": "Timer", "start": **********.544704, "duration": 5.9604644775390625e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.544747, "duration": 0.0007119178771972656}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(1 total Query, 1  unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "1.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `workflow_definitions`\n<strong>WHERE</strong> `is_active` = 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:677", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Controllers/Manage/NodeController.php:120", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Manage\\NodeController->create()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:363", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:68", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Manage/NodeController.php:120", "qid": "5a9e2fa73e431ee05268c15033a9b173"}]}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.525959, "duration": "0.005117"}, {"name": "Query", "component": "Database", "start": **********.531963, "duration": "0.001270", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `workflow_definitions`\n<strong>WHERE</strong> `is_active` = 1"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": null}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 1, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: manage/node/form.php", "component": "Views", "start": **********.536772, "duration": 0.007899045944213867}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 158 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/Array/ArrayHelper.php", "name": "ArrayHelper.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/form_helper.php", "name": "form_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/text_helper.php", "name": "text_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Log/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Log/Handlers/HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/RESTful/BaseResource.php", "name": "BaseResource.php"}, {"path": "SYSTEMPATH/RESTful/ResourceController.php", "name": "ResourceController.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Escaper/Escaper.php", "name": "Escaper.php"}, {"path": "SYSTEMPATH/ThirdParty/Escaper/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/ThirdParty/PSR/Log/LogLevel.php", "name": "LogLevel.php"}, {"path": "SYSTEMPATH/ThirdParty/PSR/Log/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/PSR/Log/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Controllers/Manage/NodeController.php", "name": "NodeController.php"}, {"path": "APPPATH/Models/NodeDefinitionModel.php", "name": "NodeDefinitionModel.php"}, {"path": "APPPATH/Models/ProjectNodeModel.php", "name": "ProjectNodeModel.php"}, {"path": "APPPATH/Models/WorkflowDefinitionModel.php", "name": "WorkflowDefinitionModel.php"}, {"path": "APPPATH/Services/NodeDefinitionService.php", "name": "NodeDefinitionService.php"}, {"path": "APPPATH/Views/manage/node/form.php", "name": "form.php"}, {"path": "FCPATH/index.php", "name": "index.php"}]}, "badgeValue": 158, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Manage\\NodeController", "method": "create", "paramCount": 0, "truePCount": 1, "params": [{"name": "$workflowId = ", "value": " <empty> | default: NULL"}]}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "api/v1/projects", "handler": "\\App\\Controllers\\Api\\V1\\ProjectController::index"}, {"method": "GET", "route": "api/v1/projects/([0-9]+)", "handler": "\\App\\Controllers\\Api\\V1\\ProjectController::show/$1"}, {"method": "GET", "route": "api/v1/projects/([0-9]+)/workflow", "handler": "\\App\\Controllers\\Api\\V1\\ProjectController::getWorkflow/$1"}, {"method": "GET", "route": "api/v1/projects/statistics", "handler": "\\App\\Controllers\\Api\\V1\\ProjectController::getStatistics"}, {"method": "GET", "route": "api/v1/workflows", "handler": "\\App\\Controllers\\Api\\V1\\WorkflowController::index"}, {"method": "GET", "route": "api/v1/workflows/([0-9]+)", "handler": "\\App\\Controllers\\Api\\V1\\WorkflowController::show/$1"}, {"method": "GET", "route": "api/v1/workflows/([0-9]+)/nodes", "handler": "\\App\\Controllers\\Api\\V1\\WorkflowController::getNodes/$1"}, {"method": "GET", "route": "api/v1/workflows/product-line/([a-zA-Z]+)", "handler": "\\App\\Controllers\\Api\\V1\\WorkflowController::getByProductLine/$1"}, {"method": "GET", "route": "api/v1/tasks", "handler": "\\App\\Controllers\\Api\\V1\\TaskController::index"}, {"method": "GET", "route": "api/v1/tasks/pending", "handler": "\\App\\Controllers\\Api\\V1\\TaskController::pending"}, {"method": "GET", "route": "api/v1/tasks/([0-9]+)", "handler": "\\App\\Controllers\\Api\\V1\\TaskController::show/$1"}, {"method": "GET", "route": "api/v1/tasks/user/([0-9]+)", "handler": "\\App\\Controllers\\Api\\V1\\TaskController::getUserTasks/$1"}, {"method": "GET", "route": "api/v1/tasks/department/([0-9]+)", "handler": "\\App\\Controllers\\Api\\V1\\TaskController::getDepartmentTasks/$1"}, {"method": "GET", "route": "api/v1/tasks/statistics", "handler": "\\App\\Controllers\\Api\\V1\\TaskController::getStatistics"}, {"method": "GET", "route": "api/v1/tasks/timeout", "handler": "\\App\\Controllers\\Api\\V1\\TaskController::getTimeoutTasks"}, {"method": "GET", "route": "manage/workflows", "handler": "\\App\\Controllers\\Manage\\WorkflowController::index"}, {"method": "GET", "route": "manage/workflows/create", "handler": "\\App\\Controllers\\Manage\\WorkflowController::create"}, {"method": "GET", "route": "manage/workflows/([0-9]+)", "handler": "\\App\\Controllers\\Manage\\WorkflowController::show/$1"}, {"method": "GET", "route": "manage/workflows/([0-9]+)/edit", "handler": "\\App\\Controllers\\Manage\\WorkflowController::edit/$1"}, {"method": "GET", "route": "manage/nodes", "handler": "\\App\\Controllers\\Manage\\NodeController::index"}, {"method": "GET", "route": "manage/nodes/workflow/([0-9]+)", "handler": "\\App\\Controllers\\Manage\\NodeController::index/$1"}, {"method": "GET", "route": "manage/nodes/create", "handler": "\\App\\Controllers\\Manage\\NodeController::create"}, {"method": "GET", "route": "manage/nodes/create/([0-9]+)", "handler": "\\App\\Controllers\\Manage\\NodeController::create/$1"}, {"method": "GET", "route": "manage/nodes/([0-9]+)", "handler": "\\App\\Controllers\\Manage\\NodeController::show/$1"}, {"method": "GET", "route": "manage/nodes/([0-9]+)/edit", "handler": "\\App\\Controllers\\Manage\\NodeController::edit/$1"}, {"method": "POST", "route": "api/v1/projects", "handler": "\\App\\Controllers\\Api\\V1\\ProjectController::create"}, {"method": "POST", "route": "api/v1/projects/([0-9]+)/workflow/start", "handler": "\\App\\Controllers\\Api\\V1\\ProjectController::startWorkflow/$1"}, {"method": "POST", "route": "api/v1/projects/([0-9]+)/collaborations", "handler": "\\App\\Controllers\\Api\\V1\\ProjectController::addCollaborations/$1"}, {"method": "POST", "route": "api/v1/projects/([0-9]+)/cancel", "handler": "\\App\\Controllers\\Api\\V1\\ProjectController::cancel/$1"}, {"method": "POST", "route": "api/v1/projects/([0-9]+)/complete", "handler": "\\App\\Controllers\\Api\\V1\\ProjectController::complete/$1"}, {"method": "POST", "route": "api/v1/workflows", "handler": "\\App\\Controllers\\Api\\V1\\WorkflowController::create"}, {"method": "POST", "route": "api/v1/workflows/([0-9]+)/nodes", "handler": "\\App\\Controllers\\Api\\V1\\WorkflowController::createNode/$1"}, {"method": "POST", "route": "api/v1/workflows/([0-9]+)/activate", "handler": "\\App\\Controllers\\Api\\V1\\WorkflowController::activate/$1"}, {"method": "POST", "route": "api/v1/workflows/([0-9]+)/deactivate", "handler": "\\App\\Controllers\\Api\\V1\\WorkflowController::deactivate/$1"}, {"method": "POST", "route": "api/v1/tasks/([0-9]+)/complete", "handler": "\\App\\Controllers\\Api\\V1\\TaskController::complete/$1"}, {"method": "POST", "route": "api/v1/tasks/([0-9]+)/reject", "handler": "\\App\\Controllers\\Api\\V1\\TaskController::reject/$1"}, {"method": "POST", "route": "api/v1/tasks/([0-9]+)/assign", "handler": "\\App\\Controllers\\Api\\V1\\TaskController::assign/$1"}, {"method": "POST", "route": "api/v1/tasks/batch-process", "handler": "\\App\\Controllers\\Api\\V1\\TaskController::batchProcess"}, {"method": "POST", "route": "manage/workflows", "handler": "\\App\\Controllers\\Manage\\WorkflowController::store"}, {"method": "POST", "route": "manage/workflows/([0-9]+)", "handler": "\\App\\Controllers\\Manage\\WorkflowController::update/$1"}, {"method": "POST", "route": "manage/workflows/([0-9]+)/activate", "handler": "\\App\\Controllers\\Manage\\WorkflowController::activate/$1"}, {"method": "POST", "route": "manage/workflows/([0-9]+)/deactivate", "handler": "\\App\\Controllers\\Manage\\WorkflowController::deactivate/$1"}, {"method": "POST", "route": "manage/workflows/([0-9]+)/copy", "handler": "\\App\\Controllers\\Manage\\WorkflowController::copy/$1"}, {"method": "POST", "route": "manage/nodes", "handler": "\\App\\Controllers\\Manage\\NodeController::store"}, {"method": "POST", "route": "manage/nodes/([0-9]+)", "handler": "\\App\\Controllers\\Manage\\NodeController::update/$1"}, {"method": "POST", "route": "manage/nodes/sort", "handler": "\\App\\Controllers\\Manage\\NodeController::sort"}, {"method": "PUT", "route": "api/v1/projects/([0-9]+)", "handler": "\\App\\Controllers\\Api\\V1\\ProjectController::update/$1"}, {"method": "PUT", "route": "api/v1/workflows/([0-9]+)", "handler": "\\App\\Controllers\\Api\\V1\\WorkflowController::update/$1"}, {"method": "PUT", "route": "manage/workflows/([0-9]+)", "handler": "\\App\\Controllers\\Manage\\WorkflowController::update/$1"}, {"method": "PUT", "route": "manage/nodes/([0-9]+)", "handler": "\\App\\Controllers\\Manage\\NodeController::update/$1"}, {"method": "DELETE", "route": "api/v1/projects/([0-9]+)", "handler": "\\App\\Controllers\\Api\\V1\\ProjectController::delete/$1"}, {"method": "DELETE", "route": "api/v1/workflows/([0-9]+)", "handler": "\\App\\Controllers\\Api\\V1\\WorkflowController::delete/$1"}, {"method": "DELETE", "route": "manage/workflows/([0-9]+)", "handler": "\\App\\Controllers\\Manage\\WorkflowController::delete/$1"}, {"method": "DELETE", "route": "manage/nodes/([0-9]+)", "handler": "\\App\\Controllers\\Manage\\NodeController::delete/$1"}]}, "badgeValue": 27, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "10.22", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.03", "count": 1}}}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.496192, "duration": 0.010217905044555664}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.533237, "duration": 3.0040740966796875e-05}]}], "vars": {"varData": {"View Data": {"workflows": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (4)</li><li>Contents (4)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>name</th><th>code</th><th>product_line</th><th>version</th><th>is_active</th><th>description</th><th>created_at</th><th>updated_at</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"UTF-8 string (19)\">&#20135;&#21697;&#32447;A&#24037;&#20316;&#27969;</td><td title=\"string (10)\">WORKFLOW_A</td><td title=\"string (1)\">A</td><td title=\"string (3)\">1.0</td><td title=\"string (1)\">1</td><td title=\"UTF-8 string (31)\">&#20135;&#21697;&#32447;A&#30340;&#23436;&#25972;&#24037;&#20316;&#27969;&#31243;</td><td title=\"string (19)\">2025-08-28 11:54:30</td><td title=\"string (19)\">2025-08-28 11:54:30</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"UTF-8 string (19)\">&#20135;&#21697;&#32447;B&#24037;&#20316;&#27969;</td><td title=\"string (10)\">WORKFLOW_B</td><td title=\"string (1)\">B</td><td title=\"string (3)\">1.0</td><td title=\"string (1)\">1</td><td title=\"UTF-8 string (31)\">&#20135;&#21697;&#32447;B&#30340;&#23436;&#25972;&#24037;&#20316;&#27969;&#31243;</td><td title=\"string (19)\">2025-08-28 11:54:30</td><td title=\"string (19)\">2025-08-28 11:54:30</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"UTF-8 string (19)\">&#20135;&#21697;&#32447;C&#24037;&#20316;&#27969;</td><td title=\"string (10)\">WORKFLOW_C</td><td title=\"string (1)\">C</td><td title=\"string (3)\">1.0</td><td title=\"string (1)\">1</td><td title=\"UTF-8 string (31)\">&#20135;&#21697;&#32447;C&#30340;&#23436;&#25972;&#24037;&#20316;&#27969;&#31243;</td><td title=\"string (19)\">2025-08-28 11:54:30</td><td title=\"string (19)\">2025-08-28 11:54:30</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"UTF-8 string (19)\">&#20135;&#21697;&#32447;D&#24037;&#20316;&#27969;</td><td title=\"string (10)\">WORKFLOW_D</td><td title=\"string (1)\">D</td><td title=\"string (3)\">1.0</td><td title=\"string (1)\">1</td><td title=\"UTF-8 string (31)\">&#20135;&#21697;&#32447;D&#30340;&#23436;&#25972;&#24037;&#20316;&#27969;&#31243;</td><td title=\"string (19)\">2025-08-28 11:54:30</td><td title=\"string (19)\">2025-08-28 11:54:30</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (19) \"&#20135;&#21697;&#32447;A&#24037;&#20316;&#27969;\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>code</dfn> =&gt; <var>string</var> (10) \"WORKFLOW_A\"<div class=\"access-path\">$value[0]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>product_line</dfn> =&gt; <var>string</var> (1) \"A\"<div class=\"access-path\">$value[0]['product_line']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>version</dfn> =&gt; <var>string</var> (3) \"1.0\"<div class=\"access-path\">$value[0]['version']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (31) \"&#20135;&#21697;&#32447;A&#30340;&#23436;&#25972;&#24037;&#20316;&#27969;&#31243;\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-08-28 11:54:30\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-08-28 11:54:30\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (19) \"&#20135;&#21697;&#32447;B&#24037;&#20316;&#27969;\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>code</dfn> =&gt; <var>string</var> (10) \"WORKFLOW_B\"<div class=\"access-path\">$value[1]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>product_line</dfn> =&gt; <var>string</var> (1) \"B\"<div class=\"access-path\">$value[1]['product_line']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>version</dfn> =&gt; <var>string</var> (3) \"1.0\"<div class=\"access-path\">$value[1]['version']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (31) \"&#20135;&#21697;&#32447;B&#30340;&#23436;&#25972;&#24037;&#20316;&#27969;&#31243;\"<div class=\"access-path\">$value[1]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-08-28 11:54:30\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-08-28 11:54:30\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (19) \"&#20135;&#21697;&#32447;C&#24037;&#20316;&#27969;\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>code</dfn> =&gt; <var>string</var> (10) \"WORKFLOW_C\"<div class=\"access-path\">$value[2]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>product_line</dfn> =&gt; <var>string</var> (1) \"C\"<div class=\"access-path\">$value[2]['product_line']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>version</dfn> =&gt; <var>string</var> (3) \"1.0\"<div class=\"access-path\">$value[2]['version']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (31) \"&#20135;&#21697;&#32447;C&#30340;&#23436;&#25972;&#24037;&#20316;&#27969;&#31243;\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-08-28 11:54:30\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-08-28 11:54:30\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (9)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (19) \"&#20135;&#21697;&#32447;D&#24037;&#20316;&#27969;\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>code</dfn> =&gt; <var>string</var> (10) \"WORKFLOW_D\"<div class=\"access-path\">$value[3]['code']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>product_line</dfn> =&gt; <var>string</var> (1) \"D\"<div class=\"access-path\">$value[3]['product_line']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>version</dfn> =&gt; <var>string</var> (3) \"1.0\"<div class=\"access-path\">$value[3]['version']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_active</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['is_active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (31) \"&#20135;&#21697;&#32447;D&#30340;&#23436;&#25972;&#24037;&#20316;&#27969;&#31243;\"<div class=\"access-path\">$value[3]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-08-28 11:54:30\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-08-28 11:54:30\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "selectedWorkflowId": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>null</var></dt></dl></div>", "node": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>null</var></dt></dl></div>", "nodeTypes": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (2)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>main</dfn> =&gt; <var>UTF-8 string</var> (9) \"&#20027;&#33410;&#28857;\"<div class=\"access-path\">$value['main']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>collaborative</dfn> =&gt; <var>UTF-8 string</var> (12) \"&#21327;&#21516;&#33410;&#28857;\"<div class=\"access-path\">$value['collaborative']</div></dt></dl></dd></dl></div>", "assigneeTypes": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (7)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sales</dfn> =&gt; <var>UTF-8 string</var> (6) \"&#38144;&#21806;\"<div class=\"access-path\">$value['sales']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>main_manager</dfn> =&gt; <var>UTF-8 string</var> (18) \"&#20027;&#37096;&#38376;&#36127;&#36131;&#20154;\"<div class=\"access-path\">$value['main_manager']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>main_executor</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#20027;&#37096;&#38376;&#25191;&#34892;&#20154;&#21592;\"<div class=\"access-path\">$value['main_executor']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>collab_manager</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#21327;&#21516;&#37096;&#38376;&#36127;&#36131;&#20154;\"<div class=\"access-path\">$value['collab_manager']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>collab_executor</dfn> =&gt; <var>UTF-8 string</var> (24) \"&#21327;&#21516;&#37096;&#38376;&#25191;&#34892;&#20154;&#21592;\"<div class=\"access-path\">$value['collab_executor']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>collab_analyst</dfn> =&gt; <var>UTF-8 string</var> (27) \"&#21327;&#21516;&#37096;&#38376;&#25968;&#25454;&#20998;&#26512;&#24072;\"<div class=\"access-path\">$value['collab_analyst']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>business</dfn> =&gt; <var>UTF-8 string</var> (6) \"&#21830;&#21153;\"<div class=\"access-path\">$value['business']</div></dt></dl></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1756358344</pre>", "_ci_previous_url": "http://adm.ws.loc/index.php/manage/nodes/create?workflow_id=1"}, "get": {"workflow_id": "1"}, "headers": {"Cookie": "debug-bar-state=minimized; ci_session=d983b3d6b8919644b8913dc82cf8f6a1", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7", "Accept-Encoding": "gzip, deflate", "Referer": "http://adm.ws.loc/manage/nodes?workflow_id=1", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Upgrade-Insecure-Requests": "1", "Connection": "keep-alive", "Host": "adm.ws.loc"}, "cookies": {"debug-bar-state": "minimized", "ci_session": "d983b3d6b8919644b8913dc82cf8f6a1"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.3", "phpVersion": "8.2.8", "phpSAPI": "fpm-fcgi", "environment": "development", "baseURL": "http://adm.ws.loc", "timezone": "UTC", "locale": "en", "cspEnabled": false}}