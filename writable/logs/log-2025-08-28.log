CRITICAL - 2025-08-28 03:39:44 --> ErrorException: Declaration of App\Controllers\Manage\WorkflowController::edit(int $id) must be compatible with CodeIgniter\RESTful\ResourceController::edit($id = null)
[Method: GET, Route: manage/workflows/]
in APPPATH/Controllers/Manage/WorkflowController.php on line 151.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-08-28 03:39:56 --> ErrorException: Declaration of App\Controllers\Manage\WorkflowController::edit(int $id) must be compatible with CodeIgniter\RESTful\ResourceController::edit($id = null)
[Method: GET, Route: manage/workflows/]
in APPPATH/Controllers/Manage/WorkflowController.php on line 151.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-08-28 03:40:12 --> ErrorException: Declaration of App\Controllers\Manage\WorkflowController::edit(int $id) must be compatible with CodeIgniter\RESTful\ResourceController::edit($id = null)
[Method: GET, Route: manage/workflows/]
in APPPATH/Controllers/Manage/WorkflowController.php on line 151.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-08-28 03:40:30 --> ErrorException: Declaration of App\Controllers\Manage\WorkflowController::edit(int $id) must be compatible with CodeIgniter\RESTful\ResourceController::edit($id = null)
[Method: GET, Route: manage/workflows/]
in APPPATH/Controllers/Manage/WorkflowController.php on line 151.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-08-28 03:41:04 --> ErrorException: Declaration of App\Controllers\Manage\WorkflowController::edit(int $id) must be compatible with CodeIgniter\RESTful\ResourceController::edit($id = null)
[Method: GET, Route: manage/workflows/]
in APPPATH/Controllers/Manage/WorkflowController.php on line 151.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-08-28 03:41:19 --> ErrorException: Declaration of App\Controllers\Manage\WorkflowController::edit(int $id) must be compatible with CodeIgniter\RESTful\ResourceController::edit($id = null)
[Method: GET, Route: manage/workflows/]
in APPPATH/Controllers/Manage/WorkflowController.php on line 151.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-08-28 03:41:37 --> ErrorException: Declaration of App\Controllers\Manage\WorkflowController::update(int $id) must be compatible with CodeIgniter\RESTful\ResourceController::update($id = null)
[Method: GET, Route: manage/workflows/]
in APPPATH/Controllers/Manage/WorkflowController.php on line 179.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
ERROR - 2025-08-28 03:42:11 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/WorkflowDefinitionService.php(62): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/WorkflowController.php(48): App\Services\WorkflowDefinitionService->getWorkflowList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\WorkflowController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:42:11 --> 获取工作流列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:42:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:42:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:44:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:44:13 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/WorkflowDefinitionService.php(62): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/WorkflowController.php(48): App\Services\WorkflowDefinitionService->getWorkflowList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\WorkflowController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:44:13 --> 获取工作流列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:44:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:44:21 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/WorkflowDefinitionService.php(62): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/WorkflowController.php(48): App\Services\WorkflowDefinitionService->getWorkflowList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\WorkflowController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:44:21 --> 获取工作流列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:44:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:44:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:44:36 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/WorkflowDefinitionService.php(62): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/WorkflowController.php(48): App\Services\WorkflowDefinitionService->getWorkflowList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\WorkflowController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:44:36 --> 获取工作流列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:44:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:44:39 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/WorkflowDefinitionService.php(62): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/WorkflowController.php(48): App\Services\WorkflowDefinitionService->getWorkflowList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\WorkflowController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:44:39 --> 获取工作流列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:44:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:45:26 --> mysqli_sql_exception: Table 'workflow_system.node_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/NodeDefinitionService.php(66): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/NodeController.php(53): App\Services\NodeDefinitionService->getNodeList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\NodeController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:45:26 --> 获取节点列表失败: Table 'workflow_system.node_definitions' doesn't exist
ERROR - 2025-08-28 03:45:26 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 /data/www/P2025/AiProjects/workflow-system-aug/system/BaseModel.php(677): CodeIgniter\Model->doFindAll()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/NodeController.php(56): CodeIgniter\BaseModel->findAll()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\NodeController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:45:26 --> 获取节点列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:45:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:45:31 --> mysqli_sql_exception: Duplicate key name 'username' in /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query('CREATE TABLE `u...', 0)
#1 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `u...')
#2 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `u...')
#3 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `u...')
#4 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/app/Database/Migrations/2025-08-28-021408_CreateWorkflowTables.php(76): CodeIgniter\Database\Forge->createTable('users', true, Array)
#5 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/MigrationRunner.php(876): App\Database\Migrations\CreateWorkflowTables->up()
#6 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/CLI/Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/CLI/Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Boot.php(388): CodeIgniter\CLI\Console->run()
#11 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Boot.php(133): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
ERROR - 2025-08-28 03:45:35 --> mysqli_sql_exception: Table 'workflow_system.node_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/NodeDefinitionService.php(66): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/NodeController.php(53): App\Services\NodeDefinitionService->getNodeList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\NodeController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:45:35 --> 获取节点列表失败: Table 'workflow_system.node_definitions' doesn't exist
ERROR - 2025-08-28 03:45:35 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 /data/www/P2025/AiProjects/workflow-system-aug/system/BaseModel.php(677): CodeIgniter\Model->doFindAll()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/NodeController.php(56): CodeIgniter\BaseModel->findAll()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\NodeController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:45:35 --> 获取节点列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:45:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:45:53 --> mysqli_sql_exception: Table 'workflow_system.node_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/NodeDefinitionService.php(66): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/NodeController.php(53): App\Services\NodeDefinitionService->getNodeList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\NodeController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:45:53 --> 获取节点列表失败: Table 'workflow_system.node_definitions' doesn't exist
ERROR - 2025-08-28 03:45:53 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 /data/www/P2025/AiProjects/workflow-system-aug/system/BaseModel.php(677): CodeIgniter\Model->doFindAll()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/NodeController.php(56): CodeIgniter\BaseModel->findAll()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\NodeController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:45:53 --> 获取节点列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:45:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:46:00 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/WorkflowDefinitionService.php(62): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/WorkflowController.php(48): App\Services\WorkflowDefinitionService->getWorkflowList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\WorkflowController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:46:00 --> 获取工作流列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:46:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:46:04 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/WorkflowDefinitionService.php(62): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/WorkflowController.php(48): App\Services\WorkflowDefinitionService->getWorkflowList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\WorkflowController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:46:04 --> 获取工作流列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:46:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:46:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:46:09 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/WorkflowDefinitionService.php(62): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/WorkflowController.php(48): App\Services\WorkflowDefinitionService->getWorkflowList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\WorkflowController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:46:09 --> 获取工作流列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:46:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:46:20 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/WorkflowDefinitionService.php(62): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/WorkflowController.php(48): App\Services\WorkflowDefinitionService->getWorkflowList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\WorkflowController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:46:20 --> 获取工作流列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:46:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:47:07 --> mysqli_sql_exception: Table 'workflow_system.workflow_definitions' doesn't exist in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(675): CodeIgniter\Database\BaseBuilder->countAllResults()
#5 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/WorkflowDefinitionService.php(62): CodeIgniter\Model->countAllResults()
#6 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/WorkflowController.php(48): App\Services\WorkflowDefinitionService->getWorkflowList()
#7 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\WorkflowController->index()
#8 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#12 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#13 {main}
ERROR - 2025-08-28 03:47:07 --> 获取工作流列表失败: Table 'workflow_system.workflow_definitions' doesn't exist
DEBUG - 2025-08-28 03:47:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 03:50:44 --> mysqli_sql_exception: Invalid default value for 'created_at' in /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query('CREATE TABLE `u...', 0)
#1 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `u...')
#2 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `u...')
#3 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `u...')
#4 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/app/Database/Migrations/2025-08-28-021408_CreateWorkflowTables.php(74): CodeIgniter\Database\Forge->createTable('users', true, Array)
#5 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/MigrationRunner.php(876): App\Database\Migrations\CreateWorkflowTables->up()
#6 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Database/MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/CLI/Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/CLI/Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Boot.php(388): CodeIgniter\CLI\Console->run()
#11 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/system/Boot.php(133): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 /Users/<USER>/projects/P2025/AiProjects/workflow-system-aug/spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-08-28 03:55:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:55:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:55:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:55:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:55:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:55:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:56:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:56:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:56:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:56:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:56:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:57:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:57:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:57:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:57:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:57:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:58:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:58:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:58:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:59:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:59:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 03:59:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:03:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:03:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:03:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:03:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:03:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:03:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:03:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:03:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:03:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:04:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:04:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 05:05:15 --> mysqli_sql_exception: Invalid JSON text: "The document is empty." at position 0 in value for column 'node_definitions.conditions'. in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 /data/www/P2025/AiProjects/workflow-system-aug/system/BaseModel.php(838): CodeIgniter\Model->doInsert()
#6 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(797): CodeIgniter\BaseModel->insert()
#7 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/NodeDefinitionService.php(194): CodeIgniter\Model->insert()
#8 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/NodeController.php(167): App\Services\NodeDefinitionService->createNode()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\NodeController->store()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#12 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#13 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#14 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#15 {main}
ERROR - 2025-08-28 05:05:15 --> 创建节点失败: Invalid JSON text: "The document is empty." at position 0 in value for column 'node_definitions.conditions'.
DEBUG - 2025-08-28 05:05:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:06:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:06:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:06:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:08:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:08:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:08:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:08:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:10:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:10:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:10:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:10:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:10:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:10:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:11:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:11:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:11:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:11:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:11:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:11:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 05:12:30 --> mysqli_sql_exception: Invalid JSON text: "The document is empty." at position 0 in value for column 'node_definitions.conditions'. in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 /data/www/P2025/AiProjects/workflow-system-aug/system/BaseModel.php(838): CodeIgniter\Model->doInsert()
#6 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(797): CodeIgniter\BaseModel->insert()
#7 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/NodeDefinitionService.php(194): CodeIgniter\Model->insert()
#8 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/NodeController.php(167): App\Services\NodeDefinitionService->createNode()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\NodeController->store()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#12 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#13 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#14 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#15 {main}
ERROR - 2025-08-28 05:12:30 --> 创建节点失败: Invalid JSON text: "The document is empty." at position 0 in value for column 'node_definitions.conditions'.
DEBUG - 2025-08-28 05:12:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:12:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 05:12:40 --> mysqli_sql_exception: Invalid JSON text: "The document is empty." at position 0 in value for column 'node_definitions.conditions'. in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 /data/www/P2025/AiProjects/workflow-system-aug/system/BaseModel.php(838): CodeIgniter\Model->doInsert()
#6 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(797): CodeIgniter\BaseModel->insert()
#7 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/NodeDefinitionService.php(194): CodeIgniter\Model->insert()
#8 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/NodeController.php(167): App\Services\NodeDefinitionService->createNode()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\NodeController->store()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#12 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#13 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#14 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#15 {main}
ERROR - 2025-08-28 05:12:40 --> 创建节点失败: Invalid JSON text: "The document is empty." at position 0 in value for column 'node_definitions.conditions'.
DEBUG - 2025-08-28 05:12:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:12:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:13:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:13:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:14:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:14:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:14:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:14:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-08-28 05:14:37 --> mysqli_sql_exception: Invalid JSON text: "The document is empty." at position 0 in value for column 'node_definitions.conditions'. in /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/MySQLi/Connection.php(327): mysqli->query()
#1 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(729): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseConnection.php(646): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /data/www/P2025/AiProjects/workflow-system-aug/system/Database/BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query()
#4 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 /data/www/P2025/AiProjects/workflow-system-aug/system/BaseModel.php(838): CodeIgniter\Model->doInsert()
#6 /data/www/P2025/AiProjects/workflow-system-aug/system/Model.php(797): CodeIgniter\BaseModel->insert()
#7 /data/www/P2025/AiProjects/workflow-system-aug/app/Services/NodeDefinitionService.php(194): CodeIgniter\Model->insert()
#8 /data/www/P2025/AiProjects/workflow-system-aug/app/Controllers/Manage/NodeController.php(167): App\Services\NodeDefinitionService->createNode()
#9 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(933): App\Controllers\Manage\NodeController->store()
#10 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController()
#11 /data/www/P2025/AiProjects/workflow-system-aug/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest()
#12 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(363): CodeIgniter\CodeIgniter->run()
#13 /data/www/P2025/AiProjects/workflow-system-aug/system/Boot.php(68): CodeIgniter\Boot::runCodeIgniter()
#14 /data/www/P2025/AiProjects/workflow-system-aug/public/index.php(59): CodeIgniter\Boot::bootWeb()
#15 {main}
ERROR - 2025-08-28 05:14:37 --> 创建节点失败: Invalid JSON text: "The document is empty." at position 0 in value for column 'node_definitions.conditions'.
DEBUG - 2025-08-28 05:14:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-08-28 05:14:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
