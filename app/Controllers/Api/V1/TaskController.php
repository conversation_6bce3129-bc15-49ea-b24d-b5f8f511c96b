<?php

namespace App\Controllers\Api\V1;

use App\Controllers\BaseController;
use App\Models\ProjectNodeModel;
use App\Services\WorkflowEngine;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * 任务处理控制器
 * 提供任务处理和管理的API接口
 */
class TaskController extends BaseController
{
    protected ProjectNodeModel $nodeModel;
    protected WorkflowEngine $workflowEngine;

    public function __construct()
    {
        $this->nodeModel = new ProjectNodeModel();
        $this->workflowEngine = new WorkflowEngine();
    }

    /**
     * 获取任务列表
     * 
     * @return ResponseInterface
     */
    public function index(): ResponseInterface
    {
        try {
            $filters = [
                'status' => $this->request->getGet('status'),
                'product_line' => $this->request->getGet('product_line'),
                'department_id' => $this->request->getGet('department_id'),
                'assignee_id' => $this->request->getGet('assignee_id'),
                'node_type' => $this->request->getGet('node_type')
            ];

            $page = (int)$this->request->getGet('page', FILTER_SANITIZE_NUMBER_INT) ?: 1;
            $perPage = (int)$this->request->getGet('per_page', FILTER_SANITIZE_NUMBER_INT) ?: 20;

            $builder = $this->nodeModel
                ->select('project_nodes.*, node_definitions.node_name, node_definitions.action_required, node_definitions.node_type, projects.title as project_title, projects.project_no, projects.product_line, users.real_name as assignee_name, departments.name as department_name')
                ->join('node_definitions', 'node_definitions.id = project_nodes.node_definition_id')
                ->join('project_workflows', 'project_workflows.id = project_nodes.project_workflow_id')
                ->join('projects', 'projects.id = project_workflows.project_id')
                ->join('users', 'users.id = project_nodes.assignee_id', 'left')
                ->join('departments', 'departments.id = project_nodes.department_id', 'left');

            // 应用筛选条件
            if (!empty($filters['status'])) {
                $builder->where('project_nodes.status', $filters['status']);
            }

            if (!empty($filters['product_line'])) {
                $builder->where('projects.product_line', $filters['product_line']);
            }

            if (!empty($filters['department_id'])) {
                $builder->where('project_nodes.department_id', $filters['department_id']);
            }

            if (!empty($filters['assignee_id'])) {
                $builder->where('project_nodes.assignee_id', $filters['assignee_id']);
            }

            if (!empty($filters['node_type'])) {
                $builder->where('node_definitions.node_type', $filters['node_type']);
            }

            // 分页
            $offset = ($page - 1) * $perPage;
            $total = $builder->countAllResults(false);
            $tasks = $builder->limit($perPage, $offset)
                           ->orderBy('project_nodes.created_at', 'DESC')
                           ->findAll();

            // 增强任务信息
            foreach ($tasks as &$task) {
                $task['action_required'] = is_string($task['action_required']) 
                    ? json_decode($task['action_required'], true) 
                    : $task['action_required'];
                
                $task['result_data'] = is_string($task['result_data']) 
                    ? json_decode($task['result_data'], true) 
                    : $task['result_data'];
            }

            return $this->respond([
                'success' => true,
                'data' => $tasks,
                'pagination' => [
                    'total' => $total,
                    'page' => $page,
                    'per_page' => $perPage,
                    'total_pages' => ceil($total / $perPage)
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取任务列表失败: ' . $e->getMessage());
            return $this->fail('获取任务列表失败');
        }
    }

    /**
     * 获取待处理任务
     * 
     * @return ResponseInterface
     */
    public function pending(): ResponseInterface
    {
        try {
            $userId = session('user_id') ?? 1; // 临时使用固定用户ID
            
            $filters = [
                'product_line' => $this->request->getGet('product_line'),
                'department_id' => $this->request->getGet('department_id'),
                'node_type' => $this->request->getGet('node_type')
            ];

            $result = $this->workflowEngine->getUserPendingTasks($userId, $filters);
            
            if (!$result['success']) {
                return $this->fail($result['message']);
            }

            return $this->respond([
                'success' => true,
                'data' => $result['data']
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取待处理任务失败: ' . $e->getMessage());
            return $this->fail('获取待处理任务失败');
        }
    }

    /**
     * 获取单个任务详情
     * 
     * @param int $id 任务ID
     * @return ResponseInterface
     */
    public function show(int $id): ResponseInterface
    {
        try {
            $task = $this->nodeModel
                ->select('project_nodes.*, node_definitions.node_name, node_definitions.action_required, node_definitions.node_type, node_definitions.can_reject, projects.title as project_title, projects.project_no, projects.product_line, users.real_name as assignee_name, departments.name as department_name')
                ->join('node_definitions', 'node_definitions.id = project_nodes.node_definition_id')
                ->join('project_workflows', 'project_workflows.id = project_nodes.project_workflow_id')
                ->join('projects', 'projects.id = project_workflows.project_id')
                ->join('users', 'users.id = project_nodes.assignee_id', 'left')
                ->join('departments', 'departments.id = project_nodes.department_id', 'left')
                ->where('project_nodes.id', $id)
                ->first();

            if (!$task) {
                return $this->failNotFound('任务不存在');
            }

            // 解析JSON字段
            $task['action_required'] = is_string($task['action_required']) 
                ? json_decode($task['action_required'], true) 
                : $task['action_required'];
            
            $task['result_data'] = is_string($task['result_data']) 
                ? json_decode($task['result_data'], true) 
                : $task['result_data'];

            return $this->respond([
                'success' => true,
                'data' => $task
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取任务详情失败: ' . $e->getMessage());
            return $this->fail('获取任务详情失败');
        }
    }

    /**
     * 完成任务
     * 
     * @param int $id 任务ID
     * @return ResponseInterface
     */
    public function complete(int $id): ResponseInterface
    {
        try {
            $task = $this->nodeModel->find($id);
            if (!$task) {
                return $this->failNotFound('任务不存在');
            }

            $data = $this->request->getJSON(true);
            $resultData = $data['result_data'] ?? [];
            $userId = session('user_id') ?? 1; // 临时使用固定用户ID

            $result = $this->workflowEngine->completeNode($id, $resultData, $userId);
            
            if (!$result['success']) {
                return $this->fail($result['message']);
            }

            return $this->respond([
                'success' => true,
                'message' => $result['message']
            ]);

        } catch (\Exception $e) {
            log_message('error', '完成任务失败: ' . $e->getMessage());
            return $this->fail('完成任务失败: ' . $e->getMessage());
        }
    }

    /**
     * 驳回任务
     * 
     * @param int $id 任务ID
     * @return ResponseInterface
     */
    public function reject(int $id): ResponseInterface
    {
        try {
            $task = $this->nodeModel->find($id);
            if (!$task) {
                return $this->failNotFound('任务不存在');
            }

            $data = $this->request->getJSON(true);
            $rejectReason = $data['reject_reason'] ?? '';
            $userId = session('user_id') ?? 1; // 临时使用固定用户ID

            if (empty($rejectReason)) {
                return $this->failValidationErrors(['reject_reason' => '请填写驳回原因']);
            }

            $result = $this->workflowEngine->rejectNode($id, $rejectReason, $userId);
            
            if (!$result['success']) {
                return $this->fail($result['message']);
            }

            return $this->respond([
                'success' => true,
                'message' => $result['message']
            ]);

        } catch (\Exception $e) {
            log_message('error', '驳回任务失败: ' . $e->getMessage());
            return $this->fail('驳回任务失败: ' . $e->getMessage());
        }
    }

    /**
     * 分配任务
     * 
     * @param int $id 任务ID
     * @return ResponseInterface
     */
    public function assign(int $id): ResponseInterface
    {
        try {
            $task = $this->nodeModel->find($id);
            if (!$task) {
                return $this->failNotFound('任务不存在');
            }

            $data = $this->request->getJSON(true);
            $assigneeId = $data['assignee_id'] ?? null;
            $operatorId = session('user_id') ?? 1; // 临时使用固定用户ID

            if (!$assigneeId) {
                return $this->failValidationErrors(['assignee_id' => '请选择处理人']);
            }

            $result = $this->workflowEngine->assignNode($id, $assigneeId, $operatorId);
            
            if (!$result['success']) {
                return $this->fail($result['message']);
            }

            return $this->respond([
                'success' => true,
                'message' => $result['message']
            ]);

        } catch (\Exception $e) {
            log_message('error', '分配任务失败: ' . $e->getMessage());
            return $this->fail('分配任务失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取用户任务
     * 
     * @param int $userId 用户ID
     * @return ResponseInterface
     */
    public function getUserTasks(int $userId): ResponseInterface
    {
        try {
            $filters = [
                'status' => $this->request->getGet('status'),
                'product_line' => $this->request->getGet('product_line'),
                'department_id' => $this->request->getGet('department_id')
            ];

            $tasks = $this->nodeModel->getPendingTasks($userId, $filters);

            return $this->respond([
                'success' => true,
                'data' => $tasks
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取用户任务失败: ' . $e->getMessage());
            return $this->fail('获取用户任务失败');
        }
    }

    /**
     * 获取部门任务
     * 
     * @param int $departmentId 部门ID
     * @return ResponseInterface
     */
    public function getDepartmentTasks(int $departmentId): ResponseInterface
    {
        try {
            $filters = [
                'status' => $this->request->getGet('status'),
                'assignee_type' => $this->request->getGet('assignee_type')
            ];

            $tasks = $this->nodeModel->getDepartmentTasks($departmentId, $filters);

            return $this->respond([
                'success' => true,
                'data' => $tasks
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取部门任务失败: ' . $e->getMessage());
            return $this->fail('获取部门任务失败');
        }
    }

    /**
     * 批量处理任务
     * 
     * @return ResponseInterface
     */
    public function batchProcess(): ResponseInterface
    {
        try {
            $data = $this->request->getJSON(true);
            $nodeActions = $data['actions'] ?? [];
            $userId = session('user_id') ?? 1; // 临时使用固定用户ID

            if (empty($nodeActions)) {
                return $this->failValidationErrors(['actions' => '请提供要处理的任务']);
            }

            $result = $this->workflowEngine->batchProcessNodes($nodeActions, $userId);
            
            if (!$result['success']) {
                return $this->fail($result['message']);
            }

            return $this->respond([
                'success' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ]);

        } catch (\Exception $e) {
            log_message('error', '批量处理任务失败: ' . $e->getMessage());
            return $this->fail('批量处理任务失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取任务统计信息
     * 
     * @return ResponseInterface
     */
    public function getStatistics(): ResponseInterface
    {
        try {
            $filters = [
                'node_type' => $this->request->getGet('node_type')
            ];

            // 日期范围筛选
            $startDate = $this->request->getGet('start_date');
            $endDate = $this->request->getGet('end_date');
            if ($startDate && $endDate) {
                $filters['date_range'] = [
                    'start' => $startDate,
                    'end' => $endDate
                ];
            }

            $statistics = $this->nodeModel->getExecutionStatistics($filters);

            return $this->respond([
                'success' => true,
                'data' => $statistics
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取任务统计失败: ' . $e->getMessage());
            return $this->fail('获取任务统计失败');
        }
    }

    /**
     * 获取超时任务
     * 
     * @return ResponseInterface
     */
    public function getTimeoutTasks(): ResponseInterface
    {
        try {
            $timeoutHours = (int)$this->request->getGet('timeout_hours', FILTER_SANITIZE_NUMBER_INT) ?: 24;
            
            $timeoutTasks = $this->nodeModel->getTimeoutNodes($timeoutHours);

            return $this->respond([
                'success' => true,
                'data' => $timeoutTasks
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取超时任务失败: ' . $e->getMessage());
            return $this->fail('获取超时任务失败');
        }
    }
}
