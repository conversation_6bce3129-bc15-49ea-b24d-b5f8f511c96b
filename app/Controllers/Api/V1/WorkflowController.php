<?php

namespace App\Controllers\Api\V1;

use App\Controllers\BaseController;
use App\Models\WorkflowDefinitionModel;
use App\Models\NodeDefinitionModel;
use App\Services\WorkflowEngine;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * 工作流管理控制器
 * 提供工作流定义和管理的API接口
 */
class WorkflowController extends BaseController
{
    protected WorkflowDefinitionModel $workflowModel;
    protected NodeDefinitionModel $nodeModel;
    protected WorkflowEngine $workflowEngine;

    public function __construct()
    {
        $this->workflowModel = new WorkflowDefinitionModel();
        $this->nodeModel = new NodeDefinitionModel();
        $this->workflowEngine = new WorkflowEngine();
    }

    /**
     * 获取工作流定义列表
     * 
     * @return ResponseInterface
     */
    public function index(): ResponseInterface
    {
        try {
            $filters = [
                'product_line' => $this->request->getGet('product_line'),
                'is_active' => $this->request->getGet('is_active'),
                'keyword' => $this->request->getGet('keyword')
            ];

            $page = (int)$this->request->getGet('page', FILTER_SANITIZE_NUMBER_INT) ?: 1;
            $perPage = (int)$this->request->getGet('per_page', FILTER_SANITIZE_NUMBER_INT) ?: 20;

            $builder = $this->workflowModel;

            // 应用筛选条件
            if (!empty($filters['product_line'])) {
                $builder = $builder->where('product_line', $filters['product_line']);
            }

            if (isset($filters['is_active']) && $filters['is_active'] !== '') {
                $builder = $builder->where('is_active', (int)$filters['is_active']);
            }

            if (!empty($filters['keyword'])) {
                $builder = $builder->groupStart()
                                 ->like('name', $filters['keyword'])
                                 ->orLike('code', $filters['keyword'])
                                 ->orLike('description', $filters['keyword'])
                                 ->groupEnd();
            }

            // 分页查询
            $offset = ($page - 1) * $perPage;
            $total = $builder->countAllResults(false);
            $workflows = $builder->limit($perPage, $offset)
                               ->orderBy('product_line', 'ASC')
                               ->orderBy('version', 'DESC')
                               ->findAll();

            // 获取每个工作流的统计信息
            foreach ($workflows as &$workflow) {
                $workflow['statistics'] = $this->workflowModel->getStatistics($workflow['id']);
                $workflow['node_count'] = $this->nodeModel->where('workflow_id', $workflow['id'])->countAllResults();
            }

            return $this->respond([
                'success' => true,
                'data' => $workflows,
                'pagination' => [
                    'total' => $total,
                    'page' => $page,
                    'per_page' => $perPage,
                    'total_pages' => ceil($total / $perPage)
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取工作流列表失败: ' . $e->getMessage());
            return $this->fail('获取工作流列表失败');
        }
    }

    /**
     * 获取单个工作流定义
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function show(int $id): ResponseInterface
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return $this->failNotFound('工作流不存在');
            }

            // 获取节点定义
            $nodes = $this->nodeModel->getWorkflowNodes($id);
            
            // 获取统计信息
            $statistics = $this->workflowModel->getStatistics($id);
            
            // 验证工作流完整性
            $validation = $this->workflowModel->validateWorkflow($id);

            $workflow['nodes'] = $nodes;
            $workflow['statistics'] = $statistics;
            $workflow['validation'] = $validation;

            return $this->respond([
                'success' => true,
                'data' => $workflow
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取工作流详情失败: ' . $e->getMessage());
            return $this->fail('获取工作流详情失败');
        }
    }

    /**
     * 创建工作流定义
     * 
     * @return ResponseInterface
     */
    public function create(): ResponseInterface
    {
        try {
            $data = $this->request->getJSON(true);

            // 验证数据
            if (!$this->workflowModel->validate($data)) {
                return $this->failValidationErrors($this->workflowModel->errors());
            }

            // 创建工作流
            $workflowId = $this->workflowModel->insert($data);
            if (!$workflowId) {
                return $this->fail('创建工作流失败');
            }

            // 如果提供了节点数据，创建节点
            if (!empty($data['nodes'])) {
                foreach ($data['nodes'] as $nodeData) {
                    $nodeData['workflow_id'] = $workflowId;
                    $this->nodeModel->createNode($nodeData);
                }
            }

            return $this->respondCreated([
                'success' => true,
                'message' => '工作流创建成功',
                'data' => ['id' => $workflowId]
            ]);

        } catch (\Exception $e) {
            log_message('error', '创建工作流失败: ' . $e->getMessage());
            return $this->fail('创建工作流失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新工作流定义
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function update(int $id): ResponseInterface
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return $this->failNotFound('工作流不存在');
            }

            $data = $this->request->getJSON(true);

            // 验证数据
            if (!$this->workflowModel->validate($data)) {
                return $this->failValidationErrors($this->workflowModel->errors());
            }

            // 更新工作流
            $success = $this->workflowModel->update($id, $data);
            if (!$success) {
                return $this->fail('更新工作流失败');
            }

            return $this->respond([
                'success' => true,
                'message' => '工作流更新成功'
            ]);

        } catch (\Exception $e) {
            log_message('error', '更新工作流失败: ' . $e->getMessage());
            return $this->fail('更新工作流失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除工作流定义
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function delete(int $id): ResponseInterface
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return $this->failNotFound('工作流不存在');
            }

            // 检查是否可以删除
            if (!$this->workflowModel->canDelete($id)) {
                return $this->fail('工作流正在使用中，无法删除');
            }

            // 删除工作流
            $success = $this->workflowModel->delete($id);
            if (!$success) {
                return $this->fail('删除工作流失败');
            }

            return $this->respond([
                'success' => true,
                'message' => '工作流删除成功'
            ]);

        } catch (\Exception $e) {
            log_message('error', '删除工作流失败: ' . $e->getMessage());
            return $this->fail('删除工作流失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取工作流的节点定义
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function getNodes(int $id): ResponseInterface
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return $this->failNotFound('工作流不存在');
            }

            $nodes = $this->nodeModel->getWorkflowNodes($id);

            // 增强节点信息
            foreach ($nodes as &$node) {
                $node['action_required'] = is_string($node['action_required']) 
                    ? json_decode($node['action_required'], true) 
                    : $node['action_required'];
                
                $node['conditions'] = is_string($node['conditions']) 
                    ? json_decode($node['conditions'], true) 
                    : $node['conditions'];
                
                $node['assignee_type_label'] = $this->nodeModel->getAssigneeTypeLabel($node['assignee_type']);
                $node['node_type_label'] = $this->nodeModel->getNodeTypeLabel($node['node_type']);
            }

            return $this->respond([
                'success' => true,
                'data' => $nodes
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取工作流节点失败: ' . $e->getMessage());
            return $this->fail('获取工作流节点失败');
        }
    }

    /**
     * 创建节点定义
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function createNode(int $id): ResponseInterface
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return $this->failNotFound('工作流不存在');
            }

            $data = $this->request->getJSON(true);
            $data['workflow_id'] = $id;

            // 验证节点配置
            $validation = $this->nodeModel->validateNodeConfig($data);
            if (!$validation['valid']) {
                return $this->failValidationErrors($validation['errors']);
            }

            // 创建节点
            $nodeId = $this->nodeModel->createNode($data);
            if (!$nodeId) {
                return $this->fail('创建节点失败');
            }

            return $this->respondCreated([
                'success' => true,
                'message' => '节点创建成功',
                'data' => ['id' => $nodeId]
            ]);

        } catch (\Exception $e) {
            log_message('error', '创建节点失败: ' . $e->getMessage());
            return $this->fail('创建节点失败: ' . $e->getMessage());
        }
    }

    /**
     * 根据产品线获取工作流定义
     * 
     * @param string $productLine 产品线
     * @return ResponseInterface
     */
    public function getByProductLine(string $productLine): ResponseInterface
    {
        try {
            if (!in_array($productLine, ['A', 'B', 'C', 'D'])) {
                return $this->failValidationErrors(['product_line' => '无效的产品线']);
            }

            $workflow = $this->workflowModel->getActiveByProductLine($productLine);
            if (!$workflow) {
                return $this->failNotFound("产品线 {$productLine} 没有可用的工作流定义");
            }

            // 获取节点定义
            $nodes = $this->nodeModel->getWorkflowNodes($workflow['id']);
            $workflow['nodes'] = $nodes;

            return $this->respond([
                'success' => true,
                'data' => $workflow
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取产品线工作流失败: ' . $e->getMessage());
            return $this->fail('获取产品线工作流失败');
        }
    }

    /**
     * 激活工作流
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function activate(int $id): ResponseInterface
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return $this->failNotFound('工作流不存在');
            }

            // 验证工作流完整性
            $validation = $this->workflowModel->validateWorkflow($id);
            if (!$validation['valid']) {
                return $this->fail('工作流配置不完整，无法激活: ' . implode(', ', $validation['errors']));
            }

            // 激活工作流
            $success = $this->workflowModel->activate($id);
            if (!$success) {
                return $this->fail('激活工作流失败');
            }

            return $this->respond([
                'success' => true,
                'message' => '工作流激活成功'
            ]);

        } catch (\Exception $e) {
            log_message('error', '激活工作流失败: ' . $e->getMessage());
            return $this->fail('激活工作流失败: ' . $e->getMessage());
        }
    }

    /**
     * 停用工作流
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function deactivate(int $id): ResponseInterface
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return $this->failNotFound('工作流不存在');
            }

            // 停用工作流
            $success = $this->workflowModel->deactivate($id);
            if (!$success) {
                return $this->fail('停用工作流失败');
            }

            return $this->respond([
                'success' => true,
                'message' => '工作流停用成功'
            ]);

        } catch (\Exception $e) {
            log_message('error', '停用工作流失败: ' . $e->getMessage());
            return $this->fail('停用工作流失败: ' . $e->getMessage());
        }
    }
}
