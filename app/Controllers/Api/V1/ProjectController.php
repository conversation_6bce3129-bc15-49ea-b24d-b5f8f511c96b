<?php

namespace App\Controllers\Api\V1;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectWorkflowModel;
use App\Services\WorkflowEngine;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * 项目管理控制器
 * 提供项目管理和工作流操作的API接口
 */
class ProjectController extends BaseController
{
    protected ProjectModel $projectModel;
    protected ProjectWorkflowModel $workflowModel;
    protected WorkflowEngine $workflowEngine;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->workflowModel = new ProjectWorkflowModel();
        $this->workflowEngine = new WorkflowEngine();
    }

    /**
     * 获取项目列表
     * 
     * @return ResponseInterface
     */
    public function index(): ResponseInterface
    {
        try {
            $filters = [
                'product_line' => $this->request->getGet('product_line'),
                'status' => $this->request->getGet('status'),
                'signing_status' => $this->request->getGet('signing_status'),
                'sales_user_id' => $this->request->getGet('sales_user_id'),
                'main_department_id' => $this->request->getGet('main_department_id'),
                'keyword' => $this->request->getGet('keyword')
            ];

            // 日期范围筛选
            $startDate = $this->request->getGet('start_date');
            $endDate = $this->request->getGet('end_date');
            if ($startDate && $endDate) {
                $filters['date_range'] = [
                    'start' => $startDate,
                    'end' => $endDate
                ];
            }

            $page = (int)$this->request->getGet('page', FILTER_SANITIZE_NUMBER_INT) ?: 1;
            $perPage = (int)$this->request->getGet('per_page', FILTER_SANITIZE_NUMBER_INT) ?: 20;

            $result = $this->projectModel->getProjectOverview($filters, $page, $perPage);

            return $this->respond([
                'success' => true,
                'data' => $result['data'],
                'pagination' => [
                    'total' => $result['total'],
                    'page' => $result['page'],
                    'per_page' => $result['per_page'],
                    'total_pages' => $result['total_pages']
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取项目列表失败: ' . $e->getMessage());
            return $this->fail('获取项目列表失败');
        }
    }

    /**
     * 获取单个项目详情
     * 
     * @param int $id 项目ID
     * @return ResponseInterface
     */
    public function show(int $id): ResponseInterface
    {
        try {
            $project = $this->projectModel->find($id);
            if (!$project) {
                return $this->failNotFound('项目不存在');
            }

            // 获取工作流状态
            $workflowStatus = $this->workflowEngine->getWorkflowStatus($id);
            if ($workflowStatus['success']) {
                $project['workflow'] = $workflowStatus['data'];
            }

            // 获取协同部门信息
            $collaborationModel = new \App\Models\ProjectCollaborationModel();
            $project['collaborations'] = $collaborationModel->getProjectCollaborations($id);

            return $this->respond([
                'success' => true,
                'data' => $project
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取项目详情失败: ' . $e->getMessage());
            return $this->fail('获取项目详情失败');
        }
    }

    /**
     * 创建项目
     * 
     * @return ResponseInterface
     */
    public function create(): ResponseInterface
    {
        try {
            $data = $this->request->getJSON(true);

            // 验证数据
            if (!$this->projectModel->validate($data)) {
                return $this->failValidationErrors($this->projectModel->errors());
            }

            // 创建项目
            $projectId = $this->projectModel->createProject($data);
            if (!$projectId) {
                return $this->fail('创建项目失败');
            }

            return $this->respondCreated([
                'success' => true,
                'message' => '项目创建成功',
                'data' => ['id' => $projectId]
            ]);

        } catch (\Exception $e) {
            log_message('error', '创建项目失败: ' . $e->getMessage());
            return $this->fail('创建项目失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新项目
     * 
     * @param int $id 项目ID
     * @return ResponseInterface
     */
    public function update(int $id): ResponseInterface
    {
        try {
            $project = $this->projectModel->find($id);
            if (!$project) {
                return $this->failNotFound('项目不存在');
            }

            $data = $this->request->getJSON(true);

            // 验证数据
            if (!$this->projectModel->validate($data)) {
                return $this->failValidationErrors($this->projectModel->errors());
            }

            // 更新项目
            $success = $this->projectModel->update($id, $data);
            if (!$success) {
                return $this->fail('更新项目失败');
            }

            return $this->respond([
                'success' => true,
                'message' => '项目更新成功'
            ]);

        } catch (\Exception $e) {
            log_message('error', '更新项目失败: ' . $e->getMessage());
            return $this->fail('更新项目失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除项目
     * 
     * @param int $id 项目ID
     * @return ResponseInterface
     */
    public function delete(int $id): ResponseInterface
    {
        try {
            $project = $this->projectModel->find($id);
            if (!$project) {
                return $this->failNotFound('项目不存在');
            }

            // 检查项目状态
            if ($project['status'] === 'active') {
                return $this->fail('活跃状态的项目无法删除');
            }

            // 删除项目
            $success = $this->projectModel->delete($id);
            if (!$success) {
                return $this->fail('删除项目失败');
            }

            return $this->respond([
                'success' => true,
                'message' => '项目删除成功'
            ]);

        } catch (\Exception $e) {
            log_message('error', '删除项目失败: ' . $e->getMessage());
            return $this->fail('删除项目失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取项目工作流状态
     * 
     * @param int $id 项目ID
     * @return ResponseInterface
     */
    public function getWorkflow(int $id): ResponseInterface
    {
        try {
            $project = $this->projectModel->find($id);
            if (!$project) {
                return $this->failNotFound('项目不存在');
            }

            $result = $this->workflowEngine->getWorkflowStatus($id);
            
            if (!$result['success']) {
                return $this->fail($result['message']);
            }

            return $this->respond([
                'success' => true,
                'data' => $result['data']
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取项目工作流状态失败: ' . $e->getMessage());
            return $this->fail('获取项目工作流状态失败');
        }
    }

    /**
     * 启动项目工作流
     * 
     * @param int $id 项目ID
     * @return ResponseInterface
     */
    public function startWorkflow(int $id): ResponseInterface
    {
        try {
            $project = $this->projectModel->find($id);
            if (!$project) {
                return $this->failNotFound('项目不存在');
            }

            $result = $this->workflowEngine->startProjectWorkflow($id);
            
            if (!$result['success']) {
                return $this->fail($result['message']);
            }

            return $this->respond([
                'success' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ]);

        } catch (\Exception $e) {
            log_message('error', '启动项目工作流失败: ' . $e->getMessage());
            return $this->fail('启动项目工作流失败: ' . $e->getMessage());
        }
    }

    /**
     * 添加协同部门
     * 
     * @param int $id 项目ID
     * @return ResponseInterface
     */
    public function addCollaborations(int $id): ResponseInterface
    {
        try {
            $project = $this->projectModel->find($id);
            if (!$project) {
                return $this->failNotFound('项目不存在');
            }

            $data = $this->request->getJSON(true);
            $departmentIds = $data['department_ids'] ?? [];

            if (empty($departmentIds)) {
                return $this->failValidationErrors(['department_ids' => '请选择协同部门']);
            }

            $userId = session('user_id') ?? 1; // 临时使用固定用户ID
            $result = $this->workflowEngine->addCollaborativeDepartments($id, $departmentIds, $userId);
            
            if (!$result['success']) {
                return $this->fail($result['message']);
            }

            return $this->respond([
                'success' => true,
                'message' => $result['message']
            ]);

        } catch (\Exception $e) {
            log_message('error', '添加协同部门失败: ' . $e->getMessage());
            return $this->fail('添加协同部门失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取项目统计信息
     * 
     * @return ResponseInterface
     */
    public function getStatistics(): ResponseInterface
    {
        try {
            $filters = [
                'sales_user_id' => $this->request->getGet('sales_user_id'),
                'main_department_id' => $this->request->getGet('main_department_id')
            ];

            // 日期范围筛选
            $startDate = $this->request->getGet('start_date');
            $endDate = $this->request->getGet('end_date');
            if ($startDate && $endDate) {
                $filters['date_range'] = [
                    'start' => $startDate,
                    'end' => $endDate
                ];
            }

            $statistics = $this->projectModel->getStatistics($filters);

            return $this->respond([
                'success' => true,
                'data' => $statistics
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取项目统计失败: ' . $e->getMessage());
            return $this->fail('获取项目统计失败');
        }
    }

    /**
     * 取消项目
     * 
     * @param int $id 项目ID
     * @return ResponseInterface
     */
    public function cancel(int $id): ResponseInterface
    {
        try {
            $project = $this->projectModel->find($id);
            if (!$project) {
                return $this->failNotFound('项目不存在');
            }

            $data = $this->request->getJSON(true);
            $reason = $data['reason'] ?? '';

            // 取消项目
            $success = $this->projectModel->cancelProject($id, $reason);
            if (!$success) {
                return $this->fail('取消项目失败');
            }

            // 如果有工作流实例，也要取消
            $workflow = $this->workflowModel->getByProjectId($id);
            if ($workflow) {
                $this->workflowModel->cancelWorkflow($workflow['id'], $reason);
            }

            return $this->respond([
                'success' => true,
                'message' => '项目已取消'
            ]);

        } catch (\Exception $e) {
            log_message('error', '取消项目失败: ' . $e->getMessage());
            return $this->fail('取消项目失败: ' . $e->getMessage());
        }
    }

    /**
     * 完成项目
     * 
     * @param int $id 项目ID
     * @return ResponseInterface
     */
    public function complete(int $id): ResponseInterface
    {
        try {
            $project = $this->projectModel->find($id);
            if (!$project) {
                return $this->failNotFound('项目不存在');
            }

            if ($project['status'] !== 'active') {
                return $this->fail('只有活跃状态的项目才能完成');
            }

            // 完成项目
            $success = $this->projectModel->completeProject($id);
            if (!$success) {
                return $this->fail('完成项目失败');
            }

            return $this->respond([
                'success' => true,
                'message' => '项目已完成'
            ]);

        } catch (\Exception $e) {
            log_message('error', '完成项目失败: ' . $e->getMessage());
            return $this->fail('完成项目失败: ' . $e->getMessage());
        }
    }
}
