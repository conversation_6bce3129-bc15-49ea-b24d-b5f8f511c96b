<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\RESTful\ResourceController;
use Psr\Log\LoggerInterface;

/**
 * 基础控制器
 * 提供通用的控制器功能和API响应方法
 */
abstract class BaseController extends ResourceController
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var array
     */
    protected $helpers = ['url', 'form', 'text'];

    /**
     * Be sure to declare properties for any property fetch you initialized.
     * The creation of dynamic property is deprecated in PHP 8.2.
     */
    // protected $session;

    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Preload any models, libraries, etc, here.

        // E.g.: $this->session = \Config\Services::session();
    }

    /**
     * 成功响应
     *
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code HTTP状态码
     * @return ResponseInterface
     */
    protected function respondSuccess($data = null, string $message = 'Success', int $code = 200): ResponseInterface
    {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        return $this->respond($response, $code);
    }

    /**
     * 错误响应
     *
     * @param string $message 错误消息
     * @param int $code HTTP状态码
     * @param mixed $errors 详细错误信息
     * @return ResponseInterface
     */
    protected function respondError(string $message = 'Error', int $code = 400, $errors = null): ResponseInterface
    {
        $response = [
            'success' => false,
            'message' => $message,
            'errors' => $errors,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        return $this->respond($response, $code);
    }

    /**
     * 验证错误响应
     *
     * @param array $errors 验证错误
     * @return ResponseInterface
     */
    protected function respondValidationError(array $errors): ResponseInterface
    {
        return $this->respondError('数据验证失败', 422, $errors);
    }

    /**
     * 未授权响应
     *
     * @param string $message 错误消息
     * @return ResponseInterface
     */
    protected function respondUnauthorized(string $message = '未授权访问'): ResponseInterface
    {
        return $this->respondError($message, 401);
    }

    /**
     * 禁止访问响应
     *
     * @param string $message 错误消息
     * @return ResponseInterface
     */
    protected function respondForbidden(string $message = '禁止访问'): ResponseInterface
    {
        return $this->respondError($message, 403);
    }

    /**
     * 资源不存在响应
     *
     * @param string $message 错误消息
     * @return ResponseInterface
     */
    protected function respondNotFound(string $message = '资源不存在'): ResponseInterface
    {
        return $this->respondError($message, 404);
    }

    /**
     * 服务器内部错误响应
     *
     * @param string $message 错误消息
     * @return ResponseInterface
     */
    protected function respondServerError(string $message = '服务器内部错误'): ResponseInterface
    {
        return $this->respondError($message, 500);
    }

    /**
     * 分页响应
     *
     * @param array $data 数据
     * @param array $pagination 分页信息
     * @param string $message 响应消息
     * @return ResponseInterface
     */
    protected function respondWithPagination(array $data, array $pagination, string $message = 'Success'): ResponseInterface
    {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data,
            'pagination' => $pagination,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        return $this->respond($response);
    }

    /**
     * 获取当前用户ID
     *
     * @return int|null
     */
    protected function getCurrentUserId(): ?int
    {
        // 这里应该从session或JWT token中获取用户ID
        // 临时返回固定值用于测试
        return session('user_id') ?? 1;
    }

    /**
     * 获取当前用户信息
     *
     * @return array|null
     */
    protected function getCurrentUser(): ?array
    {
        // 这里应该从session或缓存中获取用户信息
        // 临时返回固定值用于测试
        return session('user_data') ?? [
            'id' => 1,
            'username' => 'admin',
            'real_name' => '管理员',
            'email' => '<EMAIL>'
        ];
    }

    /**
     * 验证请求参数
     *
     * @param array $rules 验证规则
     * @param array $data 要验证的数据
     * @return bool
     */
    protected function validateRequest(array $rules, array $data = null): bool
    {
        $validation = \Config\Services::validation();

        if ($data === null) {
            $data = $this->request->getJSON(true) ?: $this->request->getPost();
        }

        return $validation->run($data, $rules);
    }

    /**
     * 获取验证错误
     *
     * @return array
     */
    protected function getValidationErrors(): array
    {
        $validation = \Config\Services::validation();
        return $validation->getErrors();
    }

    /**
     * 记录操作日志
     *
     * @param string $action 操作类型
     * @param string $description 操作描述
     * @param mixed $data 相关数据
     * @return void
     */
    protected function logAction(string $action, string $description, $data = null): void
    {
        $logData = [
            'user_id' => $this->getCurrentUserId(),
            'action' => $action,
            'description' => $description,
            'data' => $data ? json_encode($data) : null,
            'ip_address' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        log_message('info', "用户操作: {$action} - {$description}", $logData);
    }

    /**
     * 检查用户权限
     *
     * @param string $permission 权限名称
     * @return bool
     */
    protected function checkPermission(string $permission): bool
    {
        // 这里应该实现权限检查逻辑
        // 临时返回true用于测试
        return true;
    }

    /**
     * 获取请求的筛选参数
     *
     * @param array $allowedFilters 允许的筛选字段
     * @return array
     */
    protected function getFilters(array $allowedFilters): array
    {
        $filters = [];

        foreach ($allowedFilters as $filter) {
            $value = $this->request->getGet($filter);
            if ($value !== null && $value !== '') {
                $filters[$filter] = $value;
            }
        }

        return $filters;
    }

    /**
     * 获取分页参数
     *
     * @param int $defaultPerPage 默认每页数量
     * @return array
     */
    protected function getPaginationParams(int $defaultPerPage = 20): array
    {
        $page = (int)$this->request->getGet('page', FILTER_SANITIZE_NUMBER_INT) ?: 1;
        $perPage = (int)$this->request->getGet('per_page', FILTER_SANITIZE_NUMBER_INT) ?: $defaultPerPage;

        // 限制每页最大数量
        $perPage = min($perPage, 100);

        return [
            'page' => $page,
            'per_page' => $perPage,
            'offset' => ($page - 1) * $perPage
        ];
    }

    /**
     * 构建分页信息
     *
     * @param int $total 总记录数
     * @param int $page 当前页码
     * @param int $perPage 每页数量
     * @return array
     */
    protected function buildPaginationInfo(int $total, int $page, int $perPage): array
    {
        return [
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage),
            'has_next' => $page < ceil($total / $perPage),
            'has_prev' => $page > 1
        ];
    }

    /**
     * 处理异常并返回错误响应
     *
     * @param \Exception $e 异常对象
     * @param string $defaultMessage 默认错误消息
     * @return ResponseInterface
     */
    protected function handleException(\Exception $e, string $defaultMessage = '操作失败'): ResponseInterface
    {
        // 记录错误日志
        log_message('error', $e->getMessage(), [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);

        // 在开发环境显示详细错误信息
        if (ENVIRONMENT === 'development') {
            return $this->respondServerError($e->getMessage());
        }

        return $this->respondServerError($defaultMessage);
    }

    /**
     * 清理输入数据
     *
     * @param array $data 输入数据
     * @param array $allowedFields 允许的字段
     * @return array
     */
    protected function sanitizeInput(array $data, array $allowedFields): array
    {
        $sanitized = [];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $sanitized[$field] = $data[$field];
            }
        }

        return $sanitized;
    }
}
