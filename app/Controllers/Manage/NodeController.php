<?php

namespace App\Controllers\Manage;

use App\Controllers\BaseController;
use App\Models\NodeDefinitionModel;
use App\Models\WorkflowDefinitionModel;
use App\Services\NodeDefinitionService;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * 节点定义管理控制器
 * 提供节点定义的后台管理界面
 */
class NodeController extends BaseController
{
    protected NodeDefinitionModel $nodeModel;
    protected WorkflowDefinitionModel $workflowModel;
    protected NodeDefinitionService $nodeService;

    /**
     * 构造函数
     * 初始化模型和服务
     */
    public function __construct()
    {
        $this->nodeModel = new NodeDefinitionModel();
        $this->workflowModel = new WorkflowDefinitionModel();
        $this->nodeService = new NodeDefinitionService();
    }

    /**
     * 节点定义列表页面
     * 
     * @param int|null $workflowId 工作流ID
     * @return string|ResponseInterface
     */
    public function index(?int $workflowId = null)
    {
        try {
            // 获取筛选参数
            $filters = [
                'workflow_id' => $workflowId ?: $this->request->getGet('workflow_id'),
                'node_type' => $this->request->getGet('node_type'),
                'assignee_type' => $this->request->getGet('assignee_type'),
                'keyword' => $this->request->getGet('keyword')
            ];

            $page = (int)$this->request->getGet('page', FILTER_SANITIZE_NUMBER_INT) ?: 1;
            $perPage = 20;

            // 获取节点列表
            $result = $this->nodeService->getNodeList($filters, $page, $perPage);

            // 获取工作流列表用于筛选
            $workflows = $this->workflowModel->findAll();

            $viewData = [
                'nodes' => $result['data'],
                'pager' => $result['pager'],
                'filters' => $filters,
                'total' => $result['total'],
                'workflows' => $workflows,
                'nodeTypes' => ['main' => '主节点', 'collaborative' => '协同节点'],
                'assigneeTypes' => [
                    'sales' => '销售',
                    'main_manager' => '主部门负责人',
                    'main_executor' => '主部门执行人员',
                    'collab_manager' => '协同部门负责人',
                    'collab_executor' => '协同部门执行人员',
                    'collab_analyst' => '协同部门数据分析师',
                    'business' => '商务'
                ]
            ];

            return view('manage/node/index', $viewData);

        } catch (\Exception $e) {
            log_message('error', '获取节点列表失败: ' . $e->getMessage());
            return redirect()->back()->with('error', '获取节点列表失败');
        }
    }

    /**
     * 显示节点详情页面
     *
     * @param int|string|null $id 节点ID
     * @return string|ResponseInterface
     */
    public function show($id = null)
    {
        try {
            $node = $this->nodeService->getNodeDetail((int)$id);
            
            if (!$node) {
                return redirect()->to('/manage/nodes')->with('error', '节点不存在');
            }

            $viewData = [
                'node' => $node
            ];

            return view('manage/node/show', $viewData);

        } catch (\Exception $e) {
            log_message('error', '获取节点详情失败: ' . $e->getMessage());
            return redirect()->back()->with('error', '获取节点详情失败');
        }
    }

    /**
     * 显示新增节点页面
     * 
     * @param int|null $workflowId 工作流ID
     * @return string|ResponseInterface
     */
    public function create(?int $workflowId = null)
    {
        // 获取工作流列表
        $workflows = $this->workflowModel->where('is_active', 1)->findAll();

        $viewData = [
            'workflows' => $workflows,
            'selectedWorkflowId' => $workflowId,
            'node' => null,
            'nodeTypes' => ['main' => '主节点', 'collaborative' => '协同节点'],
            'assigneeTypes' => [
                'sales' => '销售',
                'main_manager' => '主部门负责人',
                'main_executor' => '主部门执行人员',
                'collab_manager' => '协同部门负责人',
                'collab_executor' => '协同部门执行人员',
                'collab_analyst' => '协同部门数据分析师',
                'business' => '商务'
            ]
        ];

        return view('manage/node/form', $viewData);
    }

    /**
     * 处理新增节点请求
     * 
     * @return ResponseInterface
     */
    public function store()
    {
        try {
            $data = $this->request->getPost();

            // 处理JSON字段
            if (isset($data['action_required']) && is_array($data['action_required'])) {
                $data['action_required'] = json_encode($data['action_required'], JSON_UNESCAPED_UNICODE);
            }
            if (isset($data['conditions']) && is_array($data['conditions'])) {
                $data['conditions'] = json_encode($data['conditions'], JSON_UNESCAPED_UNICODE);
            }

            // 验证数据
            if (!$this->nodeModel->validate($data)) {
                return redirect()->back()
                    ->withInput()
                    ->with('errors', $this->nodeModel->errors());
            }

            // 创建节点
            $result = $this->nodeService->createNode($data);
            
            if ($result['success']) {
                return redirect()->to('/manage/nodes?workflow_id=' . $data['workflow_id'])
                    ->with('success', '节点创建成功');
            } else {
                return redirect()->back()
                    ->withInput()
                    ->with('error', $result['message']);
            }

        } catch (\Exception $e) {
            log_message('error', '创建节点失败: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', '创建节点失败');
        }
    }

    /**
     * 显示编辑节点页面
     * 
     * @param int $id 节点ID
     * @return string|ResponseInterface
     */
    public function edit($id = null)
    {
        try {
            $node = $this->nodeModel->find((int)$id);
            
            if (!$node) {
                return redirect()->to('/manage/nodes')->with('error', '节点不存在');
            }

            // 解析JSON字段
            if ($node['action_required']) {
                $node['action_required'] = json_decode($node['action_required'], true);
            }
            if ($node['conditions']) {
                $node['conditions'] = json_decode($node['conditions'], true);
            }

            // 获取工作流列表
            $workflows = $this->workflowModel->where('is_active', 1)->findAll();

            $viewData = [
                'workflows' => $workflows,
                'selectedWorkflowId' => $node['workflow_id'],
                'node' => $node,
                'nodeTypes' => ['main' => '主节点', 'collaborative' => '协同节点'],
                'assigneeTypes' => [
                    'sales' => '销售',
                    'main_manager' => '主部门负责人',
                    'main_executor' => '主部门执行人员',
                    'collab_manager' => '协同部门负责人',
                    'collab_executor' => '协同部门执行人员',
                    'collab_analyst' => '协同部门数据分析师',
                    'business' => '商务'
                ]
            ];

            return view('manage/node/form', $viewData);

        } catch (\Exception $e) {
            log_message('error', '获取节点信息失败: ' . $e->getMessage());
            return redirect()->back()->with('error', '获取节点信息失败');
        }
    }

    /**
     * 处理更新节点请求
     * 
     * @param int $id 节点ID
     * @return ResponseInterface
     */
    public function update($id = null)
    {
        try {
            $data = $this->request->getPost();

            // 处理JSON字段
            if (isset($data['action_required']) && is_array($data['action_required'])) {
                $data['action_required'] = json_encode($data['action_required'], JSON_UNESCAPED_UNICODE);
            }
            if (isset($data['conditions']) && is_array($data['conditions'])) {
                $data['conditions'] = json_encode($data['conditions'], JSON_UNESCAPED_UNICODE);
            }

            // 验证数据
            if (!$this->nodeModel->validate($data)) {
                return redirect()->back()
                    ->withInput()
                    ->with('errors', $this->nodeModel->errors());
            }

            // 更新节点
            $result = $this->nodeService->updateNode((int)$id, $data);
            
            if ($result['success']) {
                return redirect()->to('/manage/nodes?workflow_id=' . $data['workflow_id'])
                    ->with('success', '节点更新成功');
            } else {
                return redirect()->back()
                    ->withInput()
                    ->with('error', $result['message']);
            }

        } catch (\Exception $e) {
            log_message('error', '更新节点失败: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', '更新节点失败');
        }
    }

    /**
     * 删除节点
     * 
     * @param int $id 节点ID
     * @return ResponseInterface
     */
    public function delete($id = null)
    {
        try {
            $result = $this->nodeService->deleteNode((int)$id);
            
            if ($result['success']) {
                return $this->respond([
                    'success' => true,
                    'message' => '节点删除成功'
                ]);
            } else {
                return $this->fail($result['message']);
            }

        } catch (\Exception $e) {
            log_message('error', '删除节点失败: ' . $e->getMessage());
            return $this->fail('删除节点失败');
        }
    }

    /**
     * 批量排序节点
     * 
     * @return ResponseInterface
     */
    public function sort()
    {
        try {
            $data = $this->request->getJSON(true);
            $result = $this->nodeService->sortNodes($data);
            
            if ($result['success']) {
                return $this->respond([
                    'success' => true,
                    'message' => '节点排序成功'
                ]);
            } else {
                return $this->fail($result['message']);
            }

        } catch (\Exception $e) {
            log_message('error', '节点排序失败: ' . $e->getMessage());
            return $this->fail('节点排序失败');
        }
    }
}
