<?php

namespace App\Controllers\Manage;

use App\Controllers\BaseController;
use App\Models\WorkflowDefinitionModel;
use App\Services\WorkflowDefinitionService;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * 工作流定义管理控制器
 * 提供工作流定义的后台管理界面
 */
class WorkflowController extends BaseController
{
    protected WorkflowDefinitionModel $workflowModel;
    protected WorkflowDefinitionService $workflowService;

    /**
     * 构造函数
     * 初始化模型和服务
     */
    public function __construct()
    {
        $this->workflowModel = new WorkflowDefinitionModel();
        $this->workflowService = new WorkflowDefinitionService();
    }

    /**
     * 工作流定义列表页面
     * 
     * @return string|ResponseInterface
     */
    public function index()
    {
        try {
            // 获取筛选参数
            $filters = [
                'product_line' => $this->request->getGet('product_line'),
                'is_active' => $this->request->getGet('is_active'),
                'keyword' => $this->request->getGet('keyword')
            ];

            $page = (int)$this->request->getGet('page', FILTER_SANITIZE_NUMBER_INT) ?: 1;
            $perPage = 20;

            // 获取工作流列表
            $result = $this->workflowService->getWorkflowList($filters, $page, $perPage);

            $viewData = [
                'workflows' => $result['data'],
                'pager' => $result['pager'],
                'filters' => $filters,
                'total' => $result['total'],
                'productLines' => ['A', 'B', 'C', 'D']
            ];

            return view('manage/workflow/index', $viewData);

        } catch (\Exception $e) {
            log_message('error', '获取工作流列表失败: ' . $e->getMessage());
            return redirect()->back()->with('error', '获取工作流列表失败');
        }
    }

    /**
     * 显示工作流详情页面
     *
     * @param int|string|null $id 工作流ID
     * @return string|ResponseInterface
     */
    public function show($id = null)
    {
        try {
            $workflow = $this->workflowService->getWorkflowDetail((int)$id);
            
            if (!$workflow) {
                return redirect()->to('/manage/workflows')->with('error', '工作流不存在');
            }

            $viewData = [
                'workflow' => $workflow
            ];

            return view('manage/workflow/show', $viewData);

        } catch (\Exception $e) {
            log_message('error', '获取工作流详情失败: ' . $e->getMessage());
            return redirect()->back()->with('error', '获取工作流详情失败');
        }
    }

    /**
     * 显示新增工作流页面
     * 
     * @return string
     */
    public function create()
    {
        $viewData = [
            'productLines' => ['A', 'B', 'C', 'D'],
            'workflow' => null
        ];

        return view('manage/workflow/form', $viewData);
    }

    /**
     * 处理新增工作流请求
     * 
     * @return ResponseInterface
     */
    public function store()
    {
        try {
            $data = $this->request->getPost();

            // 验证数据
            if (!$this->workflowModel->validate($data)) {
                return redirect()->back()
                    ->withInput()
                    ->with('errors', $this->workflowModel->errors());
            }

            // 创建工作流
            $result = $this->workflowService->createWorkflow($data);
            
            if ($result['success']) {
                return redirect()->to('/manage/workflows')
                    ->with('success', '工作流创建成功');
            } else {
                return redirect()->back()
                    ->withInput()
                    ->with('error', $result['message']);
            }

        } catch (\Exception $e) {
            log_message('error', '创建工作流失败: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', '创建工作流失败');
        }
    }

    /**
     * 显示编辑工作流页面
     * 
     * @param int $id 工作流ID
     * @return string|ResponseInterface
     */
    public function edit($id = null)
    {
        try {
            $workflow = $this->workflowModel->find((int)$id);
            
            if (!$workflow) {
                return redirect()->to('/manage/workflows')->with('error', '工作流不存在');
            }

            $viewData = [
                'productLines' => ['A', 'B', 'C', 'D'],
                'workflow' => $workflow
            ];

            return view('manage/workflow/form', $viewData);

        } catch (\Exception $e) {
            log_message('error', '获取工作流信息失败: ' . $e->getMessage());
            return redirect()->back()->with('error', '获取工作流信息失败');
        }
    }

    /**
     * 处理更新工作流请求
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function update($id = null)
    {
        try {
            $data = $this->request->getPost();

            // 验证数据
            if (!$this->workflowModel->validate($data)) {
                return redirect()->back()
                    ->withInput()
                    ->with('errors', $this->workflowModel->errors());
            }

            // 更新工作流
            $result = $this->workflowService->updateWorkflow((int)$id, $data);
            
            if ($result['success']) {
                return redirect()->to('/manage/workflows')
                    ->with('success', '工作流更新成功');
            } else {
                return redirect()->back()
                    ->withInput()
                    ->with('error', $result['message']);
            }

        } catch (\Exception $e) {
            log_message('error', '更新工作流失败: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', '更新工作流失败');
        }
    }

    /**
     * 删除工作流
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function delete($id = null)
    {
        try {
            $result = $this->workflowService->deleteWorkflow((int)$id);
            
            if ($result['success']) {
                return $this->respond([
                    'success' => true,
                    'message' => '工作流删除成功'
                ]);
            } else {
                return $this->fail($result['message']);
            }

        } catch (\Exception $e) {
            log_message('error', '删除工作流失败: ' . $e->getMessage());
            return $this->fail('删除工作流失败');
        }
    }

    /**
     * 激活工作流
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function activate($id = null)
    {
        try {
            $result = $this->workflowService->activateWorkflow((int)$id);
            
            if ($result['success']) {
                return $this->respond([
                    'success' => true,
                    'message' => '工作流激活成功'
                ]);
            } else {
                return $this->fail($result['message']);
            }

        } catch (\Exception $e) {
            log_message('error', '激活工作流失败: ' . $e->getMessage());
            return $this->fail('激活工作流失败');
        }
    }

    /**
     * 停用工作流
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function deactivate($id = null)
    {
        try {
            $result = $this->workflowService->deactivateWorkflow((int)$id);
            
            if ($result['success']) {
                return $this->respond([
                    'success' => true,
                    'message' => '工作流停用成功'
                ]);
            } else {
                return $this->fail($result['message']);
            }

        } catch (\Exception $e) {
            log_message('error', '停用工作流失败: ' . $e->getMessage());
            return $this->fail('停用工作流失败');
        }
    }

    /**
     * 复制工作流
     * 
     * @param int $id 工作流ID
     * @return ResponseInterface
     */
    public function copy($id = null)
    {
        try {
            $result = $this->workflowService->copyWorkflow((int)$id);
            
            if ($result['success']) {
                return $this->respond([
                    'success' => true,
                    'message' => '工作流复制成功',
                    'data' => ['id' => $result['data']['id']]
                ]);
            } else {
                return $this->fail($result['message']);
            }

        } catch (\Exception $e) {
            log_message('error', '复制工作流失败: ' . $e->getMessage());
            return $this->fail('复制工作流失败');
        }
    }
}
