<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// 默认路由
$routes->get('/', 'Home::index');

// API路由组 - 工作流管理系统
$routes->group('api/v1', ['namespace' => 'App\Controllers\Api\V1'], function ($routes) {

    // 项目管理路由
    $routes->group('projects', function ($routes) {
        $routes->get('/', 'ProjectController::index');
        $routes->post('/', 'ProjectController::create');
        $routes->get('(:num)', 'ProjectController::show/$1');
        $routes->put('(:num)', 'ProjectController::update/$1');
        $routes->delete('(:num)', 'ProjectController::delete/$1');
        $routes->get('(:num)/workflow', 'ProjectController::getWorkflow/$1');
        $routes->post('(:num)/workflow/start', 'ProjectController::startWorkflow/$1');
        $routes->post('(:num)/collaborations', 'ProjectController::addCollaborations/$1');
        $routes->post('(:num)/cancel', 'ProjectController::cancel/$1');
        $routes->post('(:num)/complete', 'ProjectController::complete/$1');
        $routes->get('statistics', 'ProjectController::getStatistics');
    });

    // 工作流定义管理路由
    $routes->group('workflows', function ($routes) {
        $routes->get('/', 'WorkflowController::index');
        $routes->post('/', 'WorkflowController::create');
        $routes->get('(:num)', 'WorkflowController::show/$1');
        $routes->put('(:num)', 'WorkflowController::update/$1');
        $routes->delete('(:num)', 'WorkflowController::delete/$1');
        $routes->get('(:num)/nodes', 'WorkflowController::getNodes/$1');
        $routes->post('(:num)/nodes', 'WorkflowController::createNode/$1');
        $routes->get('product-line/(:alpha)', 'WorkflowController::getByProductLine/$1');
        $routes->post('(:num)/activate', 'WorkflowController::activate/$1');
        $routes->post('(:num)/deactivate', 'WorkflowController::deactivate/$1');
    });

    // 任务处理路由
    $routes->group('tasks', function ($routes) {
        $routes->get('/', 'TaskController::index');
        $routes->get('pending', 'TaskController::pending');
        $routes->get('(:num)', 'TaskController::show/$1');
        $routes->post('(:num)/complete', 'TaskController::complete/$1');
        $routes->post('(:num)/reject', 'TaskController::reject/$1');
        $routes->post('(:num)/assign', 'TaskController::assign/$1');
        $routes->get('user/(:num)', 'TaskController::getUserTasks/$1');
        $routes->get('department/(:num)', 'TaskController::getDepartmentTasks/$1');
        $routes->post('batch-process', 'TaskController::batchProcess');
        $routes->get('statistics', 'TaskController::getStatistics');
        $routes->get('timeout', 'TaskController::getTimeoutTasks');
    });
});

// CLI路由（命令行工具）
if (is_cli()) {
    $routes->cli('migrate', 'App\Commands\MigrateCommand::index');
    $routes->cli('seed', 'App\Commands\SeedCommand::index');
    $routes->cli('workflow/init', 'App\Commands\WorkflowCommand::init');
    $routes->cli('workflow/sync', 'App\Commands\WorkflowCommand::sync');
    $routes->cli('reminder/send', 'App\Commands\ReminderCommand::send');
}
