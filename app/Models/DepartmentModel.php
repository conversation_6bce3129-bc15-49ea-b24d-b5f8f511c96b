<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * 部门模型
 * 管理部门信息和产品线关联
 */
class DepartmentModel extends Model
{
    protected $table = 'departments';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'name', 'code', 'type', 'product_lines', 'manager_id',
        'parent_id', 'sort_order', 'status', 'description'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'name' => 'required|max_length[100]',
        'code' => 'required|max_length[50]|is_unique[departments.code,id,{id}]',
        'type' => 'required|in_list[main,collaborative]',
        'status' => 'in_list[0,1]'
    ];

    /**
     * 获取支持指定产品线的协同部门
     * 
     * @param string $productLine 产品线
     * @return array
     */
    public function getCollaborativeDepartments(string $productLine): array
    {
        return $this->where('type', 'collaborative')
                   ->where("JSON_CONTAINS(product_lines, '\"$productLine\"')")
                   ->where('status', 1)
                   ->orderBy('sort_order', 'ASC')
                   ->findAll();
    }

    /**
     * 获取主部门列表
     * 
     * @param string|null $productLine 产品线（可选）
     * @return array
     */
    public function getMainDepartments(string $productLine = null): array
    {
        $builder = $this->where('type', 'main')
                       ->where('status', 1);
        
        if ($productLine) {
            $builder->where("JSON_CONTAINS(product_lines, '\"$productLine\"')");
        }
        
        return $builder->orderBy('sort_order', 'ASC')->findAll();
    }

    /**
     * 获取部门的负责人
     * 
     * @param int $departmentId 部门ID
     * @return array|null
     */
    public function getDepartmentManager(int $departmentId): ?array
    {
        $userModel = new UserModel();
        $department = $this->find($departmentId);
        
        if ($department && $department['manager_id']) {
            return $userModel->find($department['manager_id']);
        }
        
        return null;
    }

    /**
     * 获取部门成员
     * 
     * @param int $departmentId 部门ID
     * @param string|null $role 角色筛选
     * @return array
     */
    public function getDepartmentMembers(int $departmentId, string $role = null): array
    {
        $builder = $this->db->table('user_departments ud')
                           ->select('ud.*, u.real_name, u.username, u.email, u.phone')
                           ->join('users u', 'u.id = ud.user_id')
                           ->where('ud.department_id', $departmentId);
        
        if ($role) {
            $builder->where('ud.role', $role);
        }
        
        return $builder->orderBy('ud.role', 'ASC')
                      ->orderBy('u.real_name', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * 获取部门的执行人员
     * 
     * @param int $departmentId 部门ID
     * @return array
     */
    public function getDepartmentExecutors(int $departmentId): array
    {
        return $this->getDepartmentMembers($departmentId, 'executor');
    }

    /**
     * 获取部门的数据分析师
     * 
     * @param int $departmentId 部门ID
     * @return array
     */
    public function getDepartmentAnalysts(int $departmentId): array
    {
        return $this->getDepartmentMembers($departmentId, 'analyst');
    }

    /**
     * 创建部门
     * 
     * @param array $data 部门数据
     * @return int|false 部门ID或false
     */
    public function createDepartment(array $data)
    {
        // 处理产品线JSON字段
        if (isset($data['product_lines']) && is_array($data['product_lines'])) {
            $data['product_lines'] = json_encode($data['product_lines']);
        }
        
        return $this->insert($data);
    }

    /**
     * 更新部门
     * 
     * @param int $departmentId 部门ID
     * @param array $data 更新数据
     * @return bool
     */
    public function updateDepartment(int $departmentId, array $data): bool
    {
        // 处理产品线JSON字段
        if (isset($data['product_lines']) && is_array($data['product_lines'])) {
            $data['product_lines'] = json_encode($data['product_lines']);
        }
        
        return $this->update($departmentId, $data);
    }

    /**
     * 删除部门
     * 
     * @param int $departmentId 部门ID
     * @return bool
     */
    public function deleteDepartment(int $departmentId): bool
    {
        // 检查是否有项目使用此部门
        $projectModel = new ProjectModel();
        $projectCount = $projectModel->where('main_department_id', $departmentId)
                                    ->countAllResults();
        
        if ($projectCount > 0) {
            throw new \RuntimeException('无法删除正在使用的部门');
        }
        
        // 检查是否有子部门
        $childCount = $this->where('parent_id', $departmentId)->countAllResults();
        if ($childCount > 0) {
            throw new \RuntimeException('无法删除有子部门的部门');
        }
        
        return $this->delete($departmentId);
    }

    /**
     * 设置部门负责人
     * 
     * @param int $departmentId 部门ID
     * @param int $managerId 负责人ID
     * @return bool
     */
    public function setManager(int $departmentId, int $managerId): bool
    {
        // 更新部门负责人
        $result = $this->update($departmentId, ['manager_id' => $managerId]);
        
        if ($result) {
            // 更新用户部门关联表中的角色
            $userDepartmentModel = new UserDepartmentModel();
            
            // 移除原负责人角色
            $userDepartmentModel->where('department_id', $departmentId)
                               ->where('role', 'manager')
                               ->delete();
            
            // 添加新负责人角色
            $userDepartmentModel->insert([
                'user_id' => $managerId,
                'department_id' => $departmentId,
                'role' => 'manager'
            ]);
        }
        
        return $result;
    }

    /**
     * 获取部门层级结构
     * 
     * @param int|null $parentId 父部门ID
     * @return array
     */
    public function getDepartmentTree(int $parentId = null): array
    {
        $departments = $this->where('parent_id', $parentId)
                           ->where('status', 1)
                           ->orderBy('sort_order', 'ASC')
                           ->findAll();
        
        foreach ($departments as &$department) {
            $department['children'] = $this->getDepartmentTree($department['id']);
            $department['product_lines'] = is_string($department['product_lines']) 
                ? json_decode($department['product_lines'], true) 
                : $department['product_lines'];
        }
        
        return $departments;
    }

    /**
     * 获取部门统计信息
     * 
     * @param int $departmentId 部门ID
     * @return array
     */
    public function getDepartmentStatistics(int $departmentId): array
    {
        $stats = [
            'member_count' => 0,
            'project_count' => 0,
            'active_project_count' => 0,
            'completed_project_count' => 0,
            'pending_task_count' => 0
        ];
        
        // 成员数量
        $userDepartmentModel = new UserDepartmentModel();
        $stats['member_count'] = $userDepartmentModel->where('department_id', $departmentId)
                                                    ->countAllResults();
        
        // 项目数量（主部门）
        $projectModel = new ProjectModel();
        $stats['project_count'] = $projectModel->where('main_department_id', $departmentId)
                                              ->countAllResults();
        
        $stats['active_project_count'] = $projectModel->where('main_department_id', $departmentId)
                                                     ->where('status', 'active')
                                                     ->countAllResults();
        
        $stats['completed_project_count'] = $projectModel->where('main_department_id', $departmentId)
                                                        ->where('status', 'completed')
                                                        ->countAllResults();
        
        // 待处理任务数量
        $nodeModel = new ProjectNodeModel();
        $stats['pending_task_count'] = $nodeModel->where('department_id', $departmentId)
                                                ->whereIn('status', ['pending', 'running'])
                                                ->countAllResults();
        
        return $stats;
    }

    /**
     * 检查部门是否支持指定产品线
     * 
     * @param int $departmentId 部门ID
     * @param string $productLine 产品线
     * @return bool
     */
    public function supportsProductLine(int $departmentId, string $productLine): bool
    {
        $department = $this->find($departmentId);
        if (!$department) {
            return false;
        }
        
        $productLines = is_string($department['product_lines']) 
            ? json_decode($department['product_lines'], true) 
            : $department['product_lines'];
        
        return is_array($productLines) && in_array($productLine, $productLines);
    }

    /**
     * 获取部门类型标签
     * 
     * @param string $type 部门类型
     * @return string
     */
    public function getTypeLabel(string $type): string
    {
        $labels = [
            'main' => '主部门',
            'collaborative' => '协同部门'
        ];
        
        return $labels[$type] ?? $type;
    }

    /**
     * 重新排序部门
     * 
     * @param array $departmentOrders 部门ID和排序的映射 [departmentId => sortOrder]
     * @return bool
     */
    public function reorderDepartments(array $departmentOrders): bool
    {
        $this->db->transStart();
        
        foreach ($departmentOrders as $departmentId => $sortOrder) {
            $this->where('id', $departmentId)
                 ->set('sort_order', $sortOrder)
                 ->update();
        }
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }

    /**
     * 获取可选择的协同部门（排除已选择的）
     * 
     * @param string $productLine 产品线
     * @param array $excludeIds 排除的部门ID数组
     * @return array
     */
    public function getAvailableCollaborativeDepartments(string $productLine, array $excludeIds = []): array
    {
        $builder = $this->where('type', 'collaborative')
                       ->where("JSON_CONTAINS(product_lines, '\"$productLine\"')")
                       ->where('status', 1);
        
        if (!empty($excludeIds)) {
            $builder->whereNotIn('id', $excludeIds);
        }
        
        return $builder->orderBy('sort_order', 'ASC')->findAll();
    }
}
