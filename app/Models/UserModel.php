<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * 用户模型
 * 管理用户基本信息和认证
 */
class UserModel extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'username', 'email', 'password', 'real_name', 'phone', 
        'avatar', 'status', 'last_login_at'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'username' => 'required|max_length[50]|is_unique[users.username,id,{id}]',
        'email' => 'required|valid_email|max_length[100]|is_unique[users.email,id,{id}]',
        'password' => 'required|min_length[6]',
        'real_name' => 'required|max_length[50]',
        'status' => 'in_list[0,1]'
    ];
    
    protected $validationMessages = [
        'username' => [
            'required' => '用户名不能为空',
            'is_unique' => '用户名已存在'
        ],
        'email' => [
            'required' => '邮箱不能为空',
            'valid_email' => '邮箱格式不正确',
            'is_unique' => '邮箱已存在'
        ],
        'password' => [
            'required' => '密码不能为空',
            'min_length' => '密码长度不能少于6位'
        ],
        'real_name' => [
            'required' => '真实姓名不能为空'
        ]
    ];

    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * 密码哈希处理
     * 
     * @param array $data
     * @return array
     */
    protected function hashPassword(array $data): array
    {
        if (isset($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        
        return $data;
    }

    /**
     * 用户登录验证
     * 
     * @param string $username 用户名或邮箱
     * @param string $password 密码
     * @return array|null 用户信息或null
     */
    public function authenticate(string $username, string $password): ?array
    {
        $user = $this->where('username', $username)
                    ->orWhere('email', $username)
                    ->where('status', 1)
                    ->first();
        
        if ($user && password_verify($password, $user['password'])) {
            // 更新最后登录时间
            $this->update($user['id'], ['last_login_at' => date('Y-m-d H:i:s')]);
            
            // 移除密码字段
            unset($user['password']);
            
            return $user;
        }
        
        return null;
    }

    /**
     * 创建用户
     * 
     * @param array $data 用户数据
     * @return int|false 用户ID或false
     */
    public function createUser(array $data)
    {
        $data['status'] = $data['status'] ?? 1;
        return $this->insert($data);
    }

    /**
     * 更新用户信息
     * 
     * @param int $userId 用户ID
     * @param array $data 更新数据
     * @return bool
     */
    public function updateUser(int $userId, array $data): bool
    {
        // 如果没有提供密码，移除密码字段
        if (empty($data['password'])) {
            unset($data['password']);
        }
        
        return $this->update($userId, $data);
    }

    /**
     * 重置用户密码
     * 
     * @param int $userId 用户ID
     * @param string $newPassword 新密码
     * @return bool
     */
    public function resetPassword(int $userId, string $newPassword): bool
    {
        return $this->update($userId, ['password' => $newPassword]);
    }

    /**
     * 获取用户的部门信息
     * 
     * @param int $userId 用户ID
     * @return array
     */
    public function getUserDepartments(int $userId): array
    {
        return $this->db->table('user_departments ud')
                       ->select('ud.*, d.name as department_name, d.type as department_type')
                       ->join('departments d', 'd.id = ud.department_id')
                       ->where('ud.user_id', $userId)
                       ->where('d.status', 1)
                       ->orderBy('ud.is_primary', 'DESC')
                       ->orderBy('d.name', 'ASC')
                       ->get()
                       ->getResultArray();
    }

    /**
     * 获取用户的主要部门
     * 
     * @param int $userId 用户ID
     * @return array|null
     */
    public function getUserPrimaryDepartment(int $userId): ?array
    {
        $result = $this->db->table('user_departments ud')
                          ->select('ud.*, d.name as department_name, d.type as department_type')
                          ->join('departments d', 'd.id = ud.department_id')
                          ->where('ud.user_id', $userId)
                          ->where('ud.is_primary', 1)
                          ->where('d.status', 1)
                          ->get()
                          ->getRowArray();
        
        return $result ?: null;
    }

    /**
     * 检查用户是否有指定角色
     * 
     * @param int $userId 用户ID
     * @param string $role 角色
     * @param int|null $departmentId 部门ID（可选）
     * @return bool
     */
    public function hasRole(int $userId, string $role, int $departmentId = null): bool
    {
        $builder = $this->db->table('user_departments')
                           ->where('user_id', $userId)
                           ->where('role', $role);
        
        if ($departmentId) {
            $builder->where('department_id', $departmentId);
        }
        
        return $builder->countAllResults() > 0;
    }

    /**
     * 检查用户是否是部门负责人
     * 
     * @param int $userId 用户ID
     * @param int|null $departmentId 部门ID（可选）
     * @return bool
     */
    public function isDepartmentManager(int $userId, int $departmentId = null): bool
    {
        return $this->hasRole($userId, 'manager', $departmentId);
    }

    /**
     * 获取用户可访问的项目
     * 
     * @param int $userId 用户ID
     * @return array
     */
    public function getAccessibleProjects(int $userId): array
    {
        // 获取用户作为销售、商务或部门成员的项目
        $projectModel = new ProjectModel();
        
        return $projectModel->select('projects.*, departments.name as main_department_name')
                           ->join('departments', 'departments.id = projects.main_department_id', 'left')
                           ->groupStart()
                               ->where('projects.sales_user_id', $userId)
                               ->orWhere('projects.business_user_id', $userId)
                               ->orWhere('projects.main_department_id IN (SELECT department_id FROM user_departments WHERE user_id = ?)', [$userId])
                           ->groupEnd()
                           ->orderBy('projects.created_at', 'DESC')
                           ->findAll();
    }

    /**
     * 获取用户的待处理任务
     * 
     * @param int $userId 用户ID
     * @param array $filters 筛选条件
     * @return array
     */
    public function getUserPendingTasks(int $userId, array $filters = []): array
    {
        $nodeModel = new ProjectNodeModel();
        return $nodeModel->getPendingTasks($userId, $filters);
    }

    /**
     * 获取用户统计信息
     * 
     * @param int $userId 用户ID
     * @return array
     */
    public function getUserStatistics(int $userId): array
    {
        $stats = [
            'total_projects' => 0,
            'active_projects' => 0,
            'completed_projects' => 0,
            'pending_tasks' => 0,
            'completed_tasks' => 0,
            'departments_count' => 0
        ];
        
        // 项目统计
        $projectModel = new ProjectModel();
        $stats['total_projects'] = $projectModel->groupStart()
                                              ->where('sales_user_id', $userId)
                                              ->orWhere('business_user_id', $userId)
                                              ->groupEnd()
                                              ->countAllResults();
        
        $stats['active_projects'] = $projectModel->groupStart()
                                                ->where('sales_user_id', $userId)
                                                ->orWhere('business_user_id', $userId)
                                                ->groupEnd()
                                                ->where('status', 'active')
                                                ->countAllResults();
        
        $stats['completed_projects'] = $projectModel->groupStart()
                                                   ->where('sales_user_id', $userId)
                                                   ->orWhere('business_user_id', $userId)
                                                   ->groupEnd()
                                                   ->where('status', 'completed')
                                                   ->countAllResults();
        
        // 任务统计
        $nodeModel = new ProjectNodeModel();
        $stats['pending_tasks'] = $nodeModel->where('assignee_id', $userId)
                                           ->whereIn('status', ['pending', 'running'])
                                           ->countAllResults();
        
        $stats['completed_tasks'] = $nodeModel->where('assignee_id', $userId)
                                             ->where('status', 'completed')
                                             ->countAllResults();
        
        // 部门统计
        $userDepartmentModel = new UserDepartmentModel();
        $stats['departments_count'] = $userDepartmentModel->where('user_id', $userId)
                                                         ->countAllResults();
        
        return $stats;
    }

    /**
     * 搜索用户
     * 
     * @param string $keyword 关键词
     * @param array $filters 筛选条件
     * @param int $limit 限制数量
     * @return array
     */
    public function searchUsers(string $keyword, array $filters = [], int $limit = 20): array
    {
        $builder = $this->select('users.*, GROUP_CONCAT(d.name) as department_names')
                       ->join('user_departments ud', 'ud.user_id = users.id', 'left')
                       ->join('departments d', 'd.id = ud.department_id', 'left')
                       ->where('users.status', 1)
                       ->groupBy('users.id');
        
        // 关键词搜索
        if (!empty($keyword)) {
            $builder->groupStart()
                   ->like('users.real_name', $keyword)
                   ->orLike('users.username', $keyword)
                   ->orLike('users.email', $keyword)
                   ->groupEnd();
        }
        
        // 应用筛选条件
        if (!empty($filters['department_id'])) {
            $builder->where('ud.department_id', $filters['department_id']);
        }
        
        if (!empty($filters['role'])) {
            $builder->where('ud.role', $filters['role']);
        }
        
        return $builder->limit($limit)
                      ->orderBy('users.real_name', 'ASC')
                      ->findAll();
    }

    /**
     * 获取角色标签
     * 
     * @param string $role 角色
     * @return string
     */
    public function getRoleLabel(string $role): string
    {
        $labels = [
            'member' => '成员',
            'manager' => '负责人',
            'executor' => '执行人员',
            'analyst' => '数据分析师'
        ];
        
        return $labels[$role] ?? $role;
    }

    /**
     * 检查用户是否可以删除
     * 
     * @param int $userId 用户ID
     * @return bool
     */
    public function canDelete(int $userId): bool
    {
        // 检查是否有项目关联
        $projectModel = new ProjectModel();
        $projectCount = $projectModel->where('sales_user_id', $userId)
                                    ->orWhere('business_user_id', $userId)
                                    ->countAllResults();
        
        if ($projectCount > 0) {
            return false;
        }
        
        // 检查是否有任务关联
        $nodeModel = new ProjectNodeModel();
        $taskCount = $nodeModel->where('assignee_id', $userId)->countAllResults();
        
        return $taskCount === 0;
    }
}
