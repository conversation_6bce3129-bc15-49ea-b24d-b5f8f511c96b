<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * 项目模型
 * 管理项目的基本信息和状态
 */
class ProjectModel extends Model
{
    protected $table = 'projects';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'project_no', 'title', 'product_line', 'contract_amount',
        'customer_name', 'customer_contact', 'customer_phone', 'customer_email',
        'signing_status', 'main_department_id', 'sales_user_id', 'business_user_id',
        'status', 'description', 'completed_at'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'project_no' => 'required|max_length[50]|is_unique[projects.project_no,id,{id}]',
        'title' => 'required|max_length[200]',
        'product_line' => 'required|in_list[A,B,C,D]',
        'customer_name' => 'required|max_length[200]',
        'sales_user_id' => 'required|integer',
        'signing_status' => 'in_list[pending,won,lost]',
        'status' => 'in_list[draft,active,completed,cancelled]'
    ];

    /**
     * 创建项目
     * 
     * @param array $data 项目数据
     * @return int|false 项目ID或false
     */
    public function createProject(array $data)
    {
        // 生成项目编号
        if (empty($data['project_no'])) {
            $data['project_no'] = $this->generateProjectNo($data['product_line']);
        }
        
        // 自动分配主部门（除了D产品线）
        if (empty($data['main_department_id']) && $data['product_line'] !== 'D') {
            $data['main_department_id'] = $this->getMainDepartmentByProductLine($data['product_line']);
        }
        
        $data['status'] = 'draft';
        
        return $this->insert($data);
    }

    /**
     * 启动项目工作流
     * 
     * @param int $projectId 项目ID
     * @return bool
     */
    public function startWorkflow(int $projectId): bool
    {
        $project = $this->find($projectId);
        if (!$project || $project['status'] !== 'draft') {
            return false;
        }
        
        // 创建工作流实例
        $workflowModel = new ProjectWorkflowModel();
        $workflowId = $workflowModel->createProjectWorkflow($projectId, $project['product_line']);
        
        if ($workflowId) {
            // 更新项目状态为活跃
            return $this->update($projectId, ['status' => 'active']);
        }
        
        return false;
    }

    /**
     * 完成项目
     * 
     * @param int $projectId 项目ID
     * @return bool
     */
    public function completeProject(int $projectId): bool
    {
        return $this->update($projectId, [
            'status' => 'completed',
            'completed_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 取消项目
     * 
     * @param int $projectId 项目ID
     * @param string $reason 取消原因
     * @return bool
     */
    public function cancelProject(int $projectId, string $reason = ''): bool
    {
        $result = $this->update($projectId, ['status' => 'cancelled']);
        
        if ($result && $reason) {
            // 记录取消原因
            $logModel = new ProjectLogModel();
            $logModel->logAction(
                $projectId,
                null,
                'project_cancelled',
                '项目已取消',
                null,
                ['reason' => $reason]
            );
        }
        
        return $result;
    }

    /**
     * 根据产品线自动分配主部门
     * 
     * @param string $productLine 产品线
     * @return int|null 部门ID
     */
    public function getMainDepartmentByProductLine(string $productLine): ?int
    {
        $departmentModel = new DepartmentModel();
        
        // D产品线需要手动选择，返回null
        if ($productLine === 'D') {
            return null;
        }
        
        // 其他产品线自动分配
        $department = $departmentModel->where('type', 'main')
                                    ->where("JSON_CONTAINS(product_lines, '\"$productLine\"')")
                                    ->where('status', 1)
                                    ->first();
        
        return $department ? $department['id'] : null;
    }

    /**
     * 生成项目编号
     * 
     * @param string $productLine 产品线
     * @return string
     */
    public function generateProjectNo(string $productLine): string
    {
        $prefix = 'PRJ' . $productLine;
        $date = date('Ymd');
        
        // 查询当天最大序号
        $lastProject = $this->where("project_no LIKE '{$prefix}{$date}%'")
                           ->orderBy('project_no', 'DESC')
                           ->first();
        
        if ($lastProject) {
            $lastNo = substr($lastProject['project_no'], -3);
            $nextNo = str_pad((int)$lastNo + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $nextNo = '001';
        }
        
        return $prefix . $date . $nextNo;
    }

    /**
     * 获取项目概览信息
     * 
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array
     */
    public function getProjectOverview(array $filters = [], int $page = 1, int $perPage = 20): array
    {
        $builder = $this->select('projects.*, departments.name as main_department_name, u1.real_name as sales_user_name, u2.real_name as business_user_name, pw.current_node_code, pw.status as workflow_status')
                       ->join('departments', 'departments.id = projects.main_department_id', 'left')
                       ->join('users u1', 'u1.id = projects.sales_user_id', 'left')
                       ->join('users u2', 'u2.id = projects.business_user_id', 'left')
                       ->join('project_workflows pw', 'pw.project_id = projects.id', 'left');
        
        // 应用筛选条件
        if (!empty($filters['product_line'])) {
            $builder->where('projects.product_line', $filters['product_line']);
        }
        
        if (!empty($filters['status'])) {
            $builder->where('projects.status', $filters['status']);
        }
        
        if (!empty($filters['signing_status'])) {
            $builder->where('projects.signing_status', $filters['signing_status']);
        }
        
        if (!empty($filters['sales_user_id'])) {
            $builder->where('projects.sales_user_id', $filters['sales_user_id']);
        }
        
        if (!empty($filters['main_department_id'])) {
            $builder->where('projects.main_department_id', $filters['main_department_id']);
        }
        
        if (!empty($filters['date_range'])) {
            $builder->where('projects.created_at >=', $filters['date_range']['start']);
            $builder->where('projects.created_at <=', $filters['date_range']['end']);
        }
        
        if (!empty($filters['keyword'])) {
            $builder->groupStart()
                   ->like('projects.title', $filters['keyword'])
                   ->orLike('projects.project_no', $filters['keyword'])
                   ->orLike('projects.customer_name', $filters['keyword'])
                   ->groupEnd();
        }
        
        // 分页
        $offset = ($page - 1) * $perPage;
        $total = $builder->countAllResults(false);
        $projects = $builder->limit($perPage, $offset)
                           ->orderBy('projects.created_at', 'DESC')
                           ->findAll();
        
        return [
            'data' => $projects,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }

    /**
     * 获取项目统计信息
     * 
     * @param array $filters 筛选条件
     * @return array
     */
    public function getStatistics(array $filters = []): array
    {
        $builder = $this->select('status, product_line, COUNT(*) as count')
                       ->groupBy(['status', 'product_line']);
        
        // 应用筛选条件
        if (!empty($filters['date_range'])) {
            $builder->where('created_at >=', $filters['date_range']['start'])
                   ->where('created_at <=', $filters['date_range']['end']);
        }
        
        if (!empty($filters['sales_user_id'])) {
            $builder->where('sales_user_id', $filters['sales_user_id']);
        }
        
        if (!empty($filters['main_department_id'])) {
            $builder->where('main_department_id', $filters['main_department_id']);
        }
        
        $stats = $builder->findAll();
        
        $result = [
            'total' => 0,
            'by_status' => [
                'draft' => 0,
                'active' => 0,
                'completed' => 0,
                'cancelled' => 0
            ],
            'by_product_line' => [
                'A' => 0,
                'B' => 0,
                'C' => 0,
                'D' => 0
            ],
            'by_signing_status' => [
                'pending' => 0,
                'won' => 0,
                'lost' => 0
            ]
        ];
        
        foreach ($stats as $stat) {
            $result['total'] += (int)$stat['count'];
            $result['by_status'][$stat['status']] += (int)$stat['count'];
            $result['by_product_line'][$stat['product_line']] += (int)$stat['count'];
        }
        
        // 获取签约状态统计
        $signingStats = $this->select('signing_status, COUNT(*) as count')
                            ->groupBy('signing_status');
        
        if (!empty($filters['date_range'])) {
            $signingStats->where('created_at >=', $filters['date_range']['start'])
                        ->where('created_at <=', $filters['date_range']['end']);
        }
        
        $signingData = $signingStats->findAll();
        foreach ($signingData as $stat) {
            $result['by_signing_status'][$stat['signing_status']] = (int)$stat['count'];
        }
        
        return $result;
    }

    /**
     * 获取用户相关的项目
     * 
     * @param int $userId 用户ID
     * @param string $role 角色类型
     * @param array $filters 筛选条件
     * @return array
     */
    public function getUserProjects(int $userId, string $role = 'all', array $filters = []): array
    {
        $builder = $this->select('projects.*, departments.name as main_department_name');
        
        switch ($role) {
            case 'sales':
                $builder->where('projects.sales_user_id', $userId);
                break;
            case 'business':
                $builder->where('projects.business_user_id', $userId);
                break;
            case 'department':
                // 通过部门关联查找项目
                $builder->join('user_departments', 'user_departments.department_id = projects.main_department_id')
                       ->where('user_departments.user_id', $userId);
                break;
            default:
                // 查找所有相关项目
                $builder->groupStart()
                       ->where('projects.sales_user_id', $userId)
                       ->orWhere('projects.business_user_id', $userId)
                       ->groupEnd();
        }
        
        $builder->join('departments', 'departments.id = projects.main_department_id', 'left');
        
        // 应用筛选条件
        if (!empty($filters['status'])) {
            $builder->where('projects.status', $filters['status']);
        }
        
        if (!empty($filters['product_line'])) {
            $builder->where('projects.product_line', $filters['product_line']);
        }
        
        return $builder->orderBy('projects.created_at', 'DESC')->findAll();
    }
}
