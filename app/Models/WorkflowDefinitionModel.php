<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * 工作流定义模型
 * 管理工作流模板的定义和版本控制
 */
class WorkflowDefinitionModel extends Model
{
    protected $table = 'workflow_definitions';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'name', 'code', 'product_line', 'version', 'is_active', 'description'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected $validationRules = [
        'name' => 'required|max_length[100]',
        'code' => 'required|max_length[50]',
        'product_line' => 'required|in_list[A,B,C,D]',
        'version' => 'required|max_length[20]',
        'is_active' => 'in_list[0,1]'
    ];
    
    protected $validationMessages = [
        'name' => [
            'required' => '工作流名称不能为空',
            'max_length' => '工作流名称长度不能超过100个字符'
        ],
        'code' => [
            'required' => '工作流编码不能为空',
            'max_length' => '工作流编码长度不能超过50个字符'
        ],
        'product_line' => [
            'required' => '产品线不能为空',
            'in_list' => '产品线必须是A、B、C、D中的一个'
        ]
    ];

    /**
     * 根据产品线获取激活的工作流定义
     * 
     * @param string $productLine 产品线
     * @return array|null
     */
    public function getActiveByProductLine(string $productLine): ?array
    {
        return $this->where('product_line', $productLine)
                   ->where('is_active', 1)
                   ->orderBy('version', 'DESC')
                   ->first();
    }

    /**
     * 获取所有激活的工作流定义
     * 
     * @return array
     */
    public function getAllActive(): array
    {
        return $this->where('is_active', 1)
                   ->orderBy('product_line', 'ASC')
                   ->orderBy('version', 'DESC')
                   ->findAll();
    }

    /**
     * 创建新版本的工作流定义
     * 
     * @param array $data 工作流数据
     * @return int|false 新创建的工作流ID或false
     */
    public function createNewVersion(array $data)
    {
        // 检查是否存在相同编码的工作流
        $existing = $this->where('code', $data['code'])
                        ->orderBy('version', 'DESC')
                        ->first();
        
        if ($existing) {
            // 生成新版本号
            $versionParts = explode('.', $existing['version']);
            $versionParts[count($versionParts) - 1]++;
            $data['version'] = implode('.', $versionParts);
            
            // 停用旧版本
            $this->where('code', $data['code'])
                 ->set('is_active', 0)
                 ->update();
        }
        
        $data['is_active'] = 1;
        return $this->insert($data);
    }

    /**
     * 激活指定版本的工作流
     * 
     * @param int $workflowId 工作流ID
     * @return bool
     */
    public function activate(int $workflowId): bool
    {
        $workflow = $this->find($workflowId);
        if (!$workflow) {
            return false;
        }
        
        // 停用同一编码的其他版本
        $this->where('code', $workflow['code'])
             ->set('is_active', 0)
             ->update();
        
        // 激活当前版本
        return $this->update($workflowId, ['is_active' => 1]);
    }

    /**
     * 停用工作流
     * 
     * @param int $workflowId 工作流ID
     * @return bool
     */
    public function deactivate(int $workflowId): bool
    {
        return $this->update($workflowId, ['is_active' => 0]);
    }

    /**
     * 获取工作流的所有版本
     * 
     * @param string $code 工作流编码
     * @return array
     */
    public function getVersionsByCode(string $code): array
    {
        return $this->where('code', $code)
                   ->orderBy('version', 'DESC')
                   ->findAll();
    }

    /**
     * 检查工作流是否可以删除
     * 
     * @param int $workflowId 工作流ID
     * @return bool
     */
    public function canDelete(int $workflowId): bool
    {
        // 检查是否有项目正在使用此工作流
        $projectWorkflowModel = new ProjectWorkflowModel();
        $usageCount = $projectWorkflowModel->where('workflow_definition_id', $workflowId)
                                          ->countAllResults();
        
        return $usageCount === 0;
    }

    /**
     * 获取工作流统计信息
     * 
     * @param int $workflowId 工作流ID
     * @return array
     */
    public function getStatistics(int $workflowId): array
    {
        $projectWorkflowModel = new ProjectWorkflowModel();
        
        $stats = [
            'total_projects' => 0,
            'completed_projects' => 0,
            'running_projects' => 0,
            'cancelled_projects' => 0,
            'average_duration' => 0
        ];
        
        // 总项目数
        $stats['total_projects'] = $projectWorkflowModel
            ->where('workflow_definition_id', $workflowId)
            ->countAllResults();
        
        // 各状态项目数
        $statusCounts = $projectWorkflowModel
            ->select('status, COUNT(*) as count')
            ->where('workflow_definition_id', $workflowId)
            ->groupBy('status')
            ->findAll();
        
        foreach ($statusCounts as $statusCount) {
            $key = $statusCount['status'] . '_projects';
            if (isset($stats[$key])) {
                $stats[$key] = (int)$statusCount['count'];
            }
        }
        
        // 平均执行时长（已完成的项目）
        $completedProjects = $projectWorkflowModel
            ->select('TIMESTAMPDIFF(HOUR, started_at, completed_at) as duration')
            ->where('workflow_definition_id', $workflowId)
            ->where('status', 'completed')
            ->where('started_at IS NOT NULL')
            ->where('completed_at IS NOT NULL')
            ->findAll();
        
        if (!empty($completedProjects)) {
            $totalDuration = array_sum(array_column($completedProjects, 'duration'));
            $stats['average_duration'] = round($totalDuration / count($completedProjects), 2);
        }
        
        return $stats;
    }

    /**
     * 复制工作流定义
     * 
     * @param int $workflowId 源工作流ID
     * @param array $newData 新工作流数据
     * @return int|false 新工作流ID或false
     */
    public function duplicate(int $workflowId, array $newData)
    {
        $sourceWorkflow = $this->find($workflowId);
        if (!$sourceWorkflow) {
            return false;
        }
        
        // 合并数据
        $workflowData = array_merge($sourceWorkflow, $newData);
        unset($workflowData['id'], $workflowData['created_at'], $workflowData['updated_at']);
        
        // 创建新工作流
        $newWorkflowId = $this->insert($workflowData);
        
        if ($newWorkflowId) {
            // 复制节点定义
            $nodeDefinitionModel = new NodeDefinitionModel();
            $nodeDefinitionModel->duplicateNodes($workflowId, $newWorkflowId);
        }
        
        return $newWorkflowId;
    }

    /**
     * 获取产品线配置
     * 
     * @return array
     */
    public function getProductLineConfig(): array
    {
        $config = config('App');
        return $config->productLines ?? [];
    }

    /**
     * 验证工作流完整性
     * 
     * @param int $workflowId 工作流ID
     * @return array 验证结果
     */
    public function validateWorkflow(int $workflowId): array
    {
        $nodeDefinitionModel = new NodeDefinitionModel();
        $nodes = $nodeDefinitionModel->where('workflow_id', $workflowId)
                                    ->orderBy('sequence', 'ASC')
                                    ->findAll();
        
        $errors = [];
        
        if (empty($nodes)) {
            $errors[] = '工作流必须包含至少一个节点';
            return ['valid' => false, 'errors' => $errors];
        }
        
        // 检查节点序号连续性
        $sequences = array_column($nodes, 'sequence');
        sort($sequences);
        for ($i = 0; $i < count($sequences) - 1; $i++) {
            if ($sequences[$i + 1] - $sequences[$i] > 1) {
                $errors[] = "节点序号不连续：{$sequences[$i]} 到 {$sequences[$i + 1]}";
            }
        }
        
        // 检查驳回节点是否存在
        foreach ($nodes as $node) {
            if ($node['can_reject'] && $node['reject_to_node']) {
                $targetExists = false;
                foreach ($nodes as $targetNode) {
                    if ($targetNode['node_code'] === $node['reject_to_node']) {
                        $targetExists = true;
                        break;
                    }
                }
                if (!$targetExists) {
                    $errors[] = "节点 {$node['node_code']} 的驳回目标节点 {$node['reject_to_node']} 不存在";
                }
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'node_count' => count($nodes)
        ];
    }
}
