<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * 项目日志模型
 * 管理项目操作日志和审计记录
 */
class ProjectLogModel extends Model
{
    protected $table = 'project_logs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'project_id', 'node_id', 'user_id', 'action', 'action_desc',
        'old_data', 'new_data', 'ip_address', 'user_agent'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = false; // 日志不需要更新时间

    /**
     * 记录操作日志
     * 
     * @param int $projectId 项目ID
     * @param int|null $nodeId 节点ID
     * @param string $action 操作类型
     * @param string $actionDesc 操作描述
     * @param int|null $userId 操作用户ID
     * @param array|null $newData 新数据
     * @param array|null $oldData 原数据
     * @return int|false 日志ID或false
     */
    public function logAction(
        int $projectId,
        ?int $nodeId,
        string $action,
        string $actionDesc,
        ?int $userId = null,
        ?array $newData = null,
        ?array $oldData = null
    ) {
        $data = [
            'project_id' => $projectId,
            'node_id' => $nodeId,
            'user_id' => $userId,
            'action' => $action,
            'action_desc' => $actionDesc,
            'old_data' => $oldData ? json_encode($oldData) : null,
            'new_data' => $newData ? json_encode($newData) : null,
            'ip_address' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        return $this->insert($data);
    }

    /**
     * 获取项目操作日志
     * 
     * @param int $projectId 项目ID
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array
     */
    public function getProjectLogs(int $projectId, array $filters = [], int $page = 1, int $perPage = 50): array
    {
        $builder = $this->select('project_logs.*, users.real_name as user_name, project_nodes.node_code')
                       ->join('users', 'users.id = project_logs.user_id', 'left')
                       ->join('project_nodes', 'project_nodes.id = project_logs.node_id', 'left')
                       ->where('project_logs.project_id', $projectId);
        
        // 应用筛选条件
        if (!empty($filters['action'])) {
            $builder->where('project_logs.action', $filters['action']);
        }
        
        if (!empty($filters['user_id'])) {
            $builder->where('project_logs.user_id', $filters['user_id']);
        }
        
        if (!empty($filters['date_range'])) {
            $builder->where('project_logs.created_at >=', $filters['date_range']['start'])
                   ->where('project_logs.created_at <=', $filters['date_range']['end']);
        }
        
        if (!empty($filters['keyword'])) {
            $builder->like('project_logs.action_desc', $filters['keyword']);
        }
        
        // 分页
        $offset = ($page - 1) * $perPage;
        $total = $builder->countAllResults(false);
        $logs = $builder->limit($perPage, $offset)
                       ->orderBy('project_logs.created_at', 'DESC')
                       ->findAll();
        
        // 解析JSON数据
        foreach ($logs as &$log) {
            $log['old_data'] = $log['old_data'] ? json_decode($log['old_data'], true) : null;
            $log['new_data'] = $log['new_data'] ? json_decode($log['new_data'], true) : null;
        }
        
        return [
            'data' => $logs,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }

    /**
     * 获取用户操作日志
     * 
     * @param int $userId 用户ID
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array
     */
    public function getUserLogs(int $userId, array $filters = [], int $page = 1, int $perPage = 50): array
    {
        $builder = $this->select('project_logs.*, projects.title as project_title, projects.project_no, project_nodes.node_code')
                       ->join('projects', 'projects.id = project_logs.project_id')
                       ->join('project_nodes', 'project_nodes.id = project_logs.node_id', 'left')
                       ->where('project_logs.user_id', $userId);
        
        // 应用筛选条件
        if (!empty($filters['action'])) {
            $builder->where('project_logs.action', $filters['action']);
        }
        
        if (!empty($filters['project_id'])) {
            $builder->where('project_logs.project_id', $filters['project_id']);
        }
        
        if (!empty($filters['date_range'])) {
            $builder->where('project_logs.created_at >=', $filters['date_range']['start'])
                   ->where('project_logs.created_at <=', $filters['date_range']['end']);
        }
        
        // 分页
        $offset = ($page - 1) * $perPage;
        $total = $builder->countAllResults(false);
        $logs = $builder->limit($perPage, $offset)
                       ->orderBy('project_logs.created_at', 'DESC')
                       ->findAll();
        
        // 解析JSON数据
        foreach ($logs as &$log) {
            $log['old_data'] = $log['old_data'] ? json_decode($log['old_data'], true) : null;
            $log['new_data'] = $log['new_data'] ? json_decode($log['new_data'], true) : null;
        }
        
        return [
            'data' => $logs,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }

    /**
     * 获取操作类型统计
     * 
     * @param array $filters 筛选条件
     * @return array
     */
    public function getActionStatistics(array $filters = []): array
    {
        $builder = $this->select('action, COUNT(*) as count')
                       ->groupBy('action');
        
        // 应用筛选条件
        if (!empty($filters['project_id'])) {
            $builder->where('project_id', $filters['project_id']);
        }
        
        if (!empty($filters['user_id'])) {
            $builder->where('user_id', $filters['user_id']);
        }
        
        if (!empty($filters['date_range'])) {
            $builder->where('created_at >=', $filters['date_range']['start'])
                   ->where('created_at <=', $filters['date_range']['end']);
        }
        
        $stats = $builder->findAll();
        
        $result = [];
        foreach ($stats as $stat) {
            $result[$stat['action']] = (int)$stat['count'];
        }
        
        return $result;
    }

    /**
     * 获取操作频率统计（按日期）
     * 
     * @param array $filters 筛选条件
     * @param string $groupBy 分组方式：day, week, month
     * @return array
     */
    public function getActivityStatistics(array $filters = [], string $groupBy = 'day'): array
    {
        $dateFormat = match($groupBy) {
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            default => '%Y-%m-%d'
        };
        
        $builder = $this->select("DATE_FORMAT(created_at, '$dateFormat') as date_group, COUNT(*) as count")
                       ->groupBy('date_group');
        
        // 应用筛选条件
        if (!empty($filters['project_id'])) {
            $builder->where('project_id', $filters['project_id']);
        }
        
        if (!empty($filters['user_id'])) {
            $builder->where('user_id', $filters['user_id']);
        }
        
        if (!empty($filters['action'])) {
            $builder->where('action', $filters['action']);
        }
        
        if (!empty($filters['date_range'])) {
            $builder->where('created_at >=', $filters['date_range']['start'])
                   ->where('created_at <=', $filters['date_range']['end']);
        }
        
        return $builder->orderBy('date_group', 'ASC')->findAll();
    }

    /**
     * 清理过期日志
     * 
     * @param int $daysToKeep 保留天数
     * @return int 删除的记录数
     */
    public function cleanupOldLogs(int $daysToKeep = 365): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        
        $count = $this->where('created_at <', $cutoffDate)->countAllResults();
        $this->where('created_at <', $cutoffDate)->delete();
        
        return $count;
    }

    /**
     * 获取操作类型标签
     * 
     * @param string $action 操作类型
     * @return string
     */
    public function getActionLabel(string $action): string
    {
        $labels = [
            'project_created' => '项目创建',
            'project_updated' => '项目更新',
            'project_cancelled' => '项目取消',
            'project_completed' => '项目完成',
            'workflow_started' => '工作流启动',
            'workflow_completed' => '工作流完成',
            'workflow_cancelled' => '工作流取消',
            'workflow_rejected' => '工作流驳回',
            'node_assigned' => '节点分配',
            'node_started' => '节点开始',
            'node_completed' => '节点完成',
            'node_rejected' => '节点驳回',
            'file_uploaded' => '文件上传',
            'file_deleted' => '文件删除',
            'collaboration_added' => '协同部门添加',
            'collaboration_removed' => '协同部门移除',
            'user_assigned' => '用户分配',
            'user_removed' => '用户移除',
            'status_changed' => '状态变更',
            'data_updated' => '数据更新'
        ];
        
        return $labels[$action] ?? $action;
    }

    /**
     * 导出日志数据
     * 
     * @param array $filters 筛选条件
     * @param string $format 导出格式：csv, json
     * @return array
     */
    public function exportLogs(array $filters = [], string $format = 'csv'): array
    {
        $builder = $this->select('project_logs.*, users.real_name as user_name, projects.title as project_title, projects.project_no')
                       ->join('users', 'users.id = project_logs.user_id', 'left')
                       ->join('projects', 'projects.id = project_logs.project_id');
        
        // 应用筛选条件
        if (!empty($filters['project_id'])) {
            $builder->where('project_logs.project_id', $filters['project_id']);
        }
        
        if (!empty($filters['user_id'])) {
            $builder->where('project_logs.user_id', $filters['user_id']);
        }
        
        if (!empty($filters['action'])) {
            $builder->where('project_logs.action', $filters['action']);
        }
        
        if (!empty($filters['date_range'])) {
            $builder->where('project_logs.created_at >=', $filters['date_range']['start'])
                   ->where('project_logs.created_at <=', $filters['date_range']['end']);
        }
        
        $logs = $builder->orderBy('project_logs.created_at', 'DESC')->findAll();
        
        // 处理数据格式
        foreach ($logs as &$log) {
            $log['action_label'] = $this->getActionLabel($log['action']);
            $log['old_data'] = $log['old_data'] ? json_decode($log['old_data'], true) : null;
            $log['new_data'] = $log['new_data'] ? json_decode($log['new_data'], true) : null;
        }
        
        return $logs;
    }

    /**
     * 获取客户端IP地址
     * 
     * @return string
     */
    private function getClientIP(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // 处理多个IP的情况（X-Forwarded-For可能包含多个IP）
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // 验证IP格式
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * 批量记录日志
     * 
     * @param array $logs 日志数据数组
     * @return bool
     */
    public function batchLogActions(array $logs): bool
    {
        $this->db->transStart();
        
        foreach ($logs as $log) {
            $this->logAction(
                $log['project_id'],
                $log['node_id'] ?? null,
                $log['action'],
                $log['action_desc'],
                $log['user_id'] ?? null,
                $log['new_data'] ?? null,
                $log['old_data'] ?? null
            );
        }
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }
}
