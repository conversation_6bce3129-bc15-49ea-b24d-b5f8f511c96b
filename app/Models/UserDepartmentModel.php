<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * 用户部门关联模型
 * 管理用户与部门的关联关系和角色
 */
class UserDepartmentModel extends Model
{
    protected $table = 'user_departments';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'user_id', 'department_id', 'role', 'is_primary'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected $validationRules = [
        'user_id' => 'required|integer',
        'department_id' => 'required|integer',
        'role' => 'required|in_list[member,manager,executor,analyst]',
        'is_primary' => 'in_list[0,1]'
    ];

    /**
     * 分配用户到部门
     * 
     * @param int $userId 用户ID
     * @param int $departmentId 部门ID
     * @param string $role 角色
     * @param bool $isPrimary 是否为主要部门
     * @return int|false 关联ID或false
     */
    public function assignUserToDepartment(int $userId, int $departmentId, string $role = 'member', bool $isPrimary = false)
    {
        // 检查是否已存在相同的关联
        $existing = $this->where('user_id', $userId)
                        ->where('department_id', $departmentId)
                        ->where('role', $role)
                        ->first();
        
        if ($existing) {
            throw new \RuntimeException('用户已在该部门担任此角色');
        }
        
        // 如果设置为主要部门，先取消其他主要部门
        if ($isPrimary) {
            $this->where('user_id', $userId)
                 ->set('is_primary', 0)
                 ->update();
        }
        
        $data = [
            'user_id' => $userId,
            'department_id' => $departmentId,
            'role' => $role,
            'is_primary' => $isPrimary ? 1 : 0
        ];
        
        return $this->insert($data);
    }

    /**
     * 移除用户部门关联
     * 
     * @param int $userId 用户ID
     * @param int $departmentId 部门ID
     * @param string|null $role 角色（可选）
     * @return bool
     */
    public function removeUserFromDepartment(int $userId, int $departmentId, string $role = null): bool
    {
        $builder = $this->where('user_id', $userId)
                       ->where('department_id', $departmentId);
        
        if ($role) {
            $builder->where('role', $role);
        }
        
        return $builder->delete();
    }

    /**
     * 更新用户在部门中的角色
     * 
     * @param int $userId 用户ID
     * @param int $departmentId 部门ID
     * @param string $oldRole 原角色
     * @param string $newRole 新角色
     * @return bool
     */
    public function updateUserRole(int $userId, int $departmentId, string $oldRole, string $newRole): bool
    {
        return $this->where('user_id', $userId)
                   ->where('department_id', $departmentId)
                   ->where('role', $oldRole)
                   ->set('role', $newRole)
                   ->update();
    }

    /**
     * 设置用户的主要部门
     * 
     * @param int $userId 用户ID
     * @param int $departmentId 部门ID
     * @return bool
     */
    public function setPrimaryDepartment(int $userId, int $departmentId): bool
    {
        // 先取消所有主要部门标记
        $this->where('user_id', $userId)
             ->set('is_primary', 0)
             ->update();
        
        // 设置新的主要部门
        return $this->where('user_id', $userId)
                   ->where('department_id', $departmentId)
                   ->set('is_primary', 1)
                   ->update();
    }

    /**
     * 获取部门的用户列表
     * 
     * @param int $departmentId 部门ID
     * @param string|null $role 角色筛选
     * @return array
     */
    public function getDepartmentUsers(int $departmentId, string $role = null): array
    {
        $builder = $this->select('user_departments.*, users.real_name, users.username, users.email, users.phone, users.status')
                       ->join('users', 'users.id = user_departments.user_id')
                       ->where('user_departments.department_id', $departmentId)
                       ->where('users.status', 1);
        
        if ($role) {
            $builder->where('user_departments.role', $role);
        }
        
        return $builder->orderBy('user_departments.role', 'ASC')
                      ->orderBy('users.real_name', 'ASC')
                      ->findAll();
    }

    /**
     * 获取用户的部门列表
     * 
     * @param int $userId 用户ID
     * @param string|null $role 角色筛选
     * @return array
     */
    public function getUserDepartments(int $userId, string $role = null): array
    {
        $builder = $this->select('user_departments.*, departments.name as department_name, departments.type as department_type, departments.code as department_code')
                       ->join('departments', 'departments.id = user_departments.department_id')
                       ->where('user_departments.user_id', $userId)
                       ->where('departments.status', 1);
        
        if ($role) {
            $builder->where('user_departments.role', $role);
        }
        
        return $builder->orderBy('user_departments.is_primary', 'DESC')
                      ->orderBy('departments.name', 'ASC')
                      ->findAll();
    }

    /**
     * 检查用户是否在指定部门担任指定角色
     * 
     * @param int $userId 用户ID
     * @param int $departmentId 部门ID
     * @param string $role 角色
     * @return bool
     */
    public function hasUserRole(int $userId, int $departmentId, string $role): bool
    {
        return $this->where('user_id', $userId)
                   ->where('department_id', $departmentId)
                   ->where('role', $role)
                   ->countAllResults() > 0;
    }

    /**
     * 获取部门的负责人
     * 
     * @param int $departmentId 部门ID
     * @return array|null
     */
    public function getDepartmentManager(int $departmentId): ?array
    {
        $result = $this->select('user_departments.*, users.real_name, users.username, users.email, users.phone')
                      ->join('users', 'users.id = user_departments.user_id')
                      ->where('user_departments.department_id', $departmentId)
                      ->where('user_departments.role', 'manager')
                      ->where('users.status', 1)
                      ->first();
        
        return $result ?: null;
    }

    /**
     * 获取部门的执行人员列表
     * 
     * @param int $departmentId 部门ID
     * @return array
     */
    public function getDepartmentExecutors(int $departmentId): array
    {
        return $this->getDepartmentUsers($departmentId, 'executor');
    }

    /**
     * 获取部门的数据分析师列表
     * 
     * @param int $departmentId 部门ID
     * @return array
     */
    public function getDepartmentAnalysts(int $departmentId): array
    {
        return $this->getDepartmentUsers($departmentId, 'analyst');
    }

    /**
     * 批量分配用户到部门
     * 
     * @param array $assignments 分配数据 [['user_id' => 1, 'department_id' => 1, 'role' => 'member'], ...]
     * @return bool
     */
    public function batchAssignUsers(array $assignments): bool
    {
        $this->db->transStart();
        
        foreach ($assignments as $assignment) {
            $this->assignUserToDepartment(
                $assignment['user_id'],
                $assignment['department_id'],
                $assignment['role'] ?? 'member',
                $assignment['is_primary'] ?? false
            );
        }
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }

    /**
     * 获取可分配给指定角色的用户列表
     * 
     * @param int $departmentId 部门ID
     * @param string $role 角色
     * @return array
     */
    public function getAvailableUsersForRole(int $departmentId, string $role): array
    {
        // 获取已在该部门担任该角色的用户ID
        $existingUserIds = $this->select('user_id')
                               ->where('department_id', $departmentId)
                               ->where('role', $role)
                               ->findColumn('user_id');
        
        // 获取可用用户
        $userModel = new UserModel();
        $builder = $userModel->select('id, real_name, username, email')
                            ->where('status', 1);
        
        if (!empty($existingUserIds)) {
            $builder->whereNotIn('id', $existingUserIds);
        }
        
        return $builder->orderBy('real_name', 'ASC')->findAll();
    }

    /**
     * 获取用户在各部门的角色统计
     * 
     * @param int $userId 用户ID
     * @return array
     */
    public function getUserRoleStatistics(int $userId): array
    {
        $roles = $this->select('role, COUNT(*) as count')
                     ->where('user_id', $userId)
                     ->groupBy('role')
                     ->findAll();
        
        $stats = [
            'member' => 0,
            'manager' => 0,
            'executor' => 0,
            'analyst' => 0,
            'total' => 0
        ];
        
        foreach ($roles as $role) {
            $stats[$role['role']] = (int)$role['count'];
            $stats['total'] += (int)$role['count'];
        }
        
        return $stats;
    }

    /**
     * 获取部门的角色分布统计
     * 
     * @param int $departmentId 部门ID
     * @return array
     */
    public function getDepartmentRoleStatistics(int $departmentId): array
    {
        $roles = $this->select('role, COUNT(*) as count')
                     ->where('department_id', $departmentId)
                     ->groupBy('role')
                     ->findAll();
        
        $stats = [
            'member' => 0,
            'manager' => 0,
            'executor' => 0,
            'analyst' => 0,
            'total' => 0
        ];
        
        foreach ($roles as $role) {
            $stats[$role['role']] = (int)$role['count'];
            $stats['total'] += (int)$role['count'];
        }
        
        return $stats;
    }

    /**
     * 检查用户是否可以从部门移除
     * 
     * @param int $userId 用户ID
     * @param int $departmentId 部门ID
     * @param string $role 角色
     * @return bool
     */
    public function canRemoveUserFromDepartment(int $userId, int $departmentId, string $role): bool
    {
        // 如果是负责人角色，检查是否有待处理的任务
        if ($role === 'manager') {
            $nodeModel = new ProjectNodeModel();
            $pendingTasks = $nodeModel->where('assignee_id', $userId)
                                     ->where('department_id', $departmentId)
                                     ->whereIn('status', ['pending', 'running'])
                                     ->countAllResults();
            
            return $pendingTasks === 0;
        }
        
        return true;
    }

    /**
     * 获取角色权限映射
     * 
     * @return array
     */
    public function getRolePermissions(): array
    {
        return [
            'member' => [
                'view_department_projects',
                'view_department_tasks'
            ],
            'manager' => [
                'view_department_projects',
                'view_department_tasks',
                'assign_tasks',
                'approve_tasks',
                'manage_department_members'
            ],
            'executor' => [
                'view_department_projects',
                'view_department_tasks',
                'execute_tasks',
                'upload_files'
            ],
            'analyst' => [
                'view_department_projects',
                'view_department_tasks',
                'analyze_data',
                'upload_data'
            ]
        ];
    }
}
