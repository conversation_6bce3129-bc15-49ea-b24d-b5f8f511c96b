<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * 项目协同部门模型
 * 管理项目与协同部门的关联关系
 */
class ProjectCollaborationModel extends Model
{
    protected $table = 'project_collaborations';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'project_id', 'department_id', 'collaboration_content',
        'status', 'assigned_at', 'completed_at'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected $validationRules = [
        'project_id' => 'required|integer',
        'department_id' => 'required|integer',
        'status' => 'in_list[pending,active,completed,cancelled]'
    ];

    /**
     * 获取项目的协同部门
     * 
     * @param int $projectId 项目ID
     * @param string|null $status 状态筛选
     * @return array
     */
    public function getProjectCollaborations(int $projectId, string $status = null): array
    {
        $builder = $this->select('project_collaborations.*, departments.name as department_name, departments.code as department_code')
                       ->join('departments', 'departments.id = project_collaborations.department_id')
                       ->where('project_collaborations.project_id', $projectId);
        
        if ($status) {
            $builder->where('project_collaborations.status', $status);
        }
        
        return $builder->orderBy('project_collaborations.assigned_at', 'ASC')->findAll();
    }

    /**
     * 获取部门参与的项目
     * 
     * @param int $departmentId 部门ID
     * @param string|null $status 状态筛选
     * @return array
     */
    public function getDepartmentProjects(int $departmentId, string $status = null): array
    {
        $builder = $this->select('project_collaborations.*, projects.title as project_title, projects.project_no, projects.product_line')
                       ->join('projects', 'projects.id = project_collaborations.project_id')
                       ->where('project_collaborations.department_id', $departmentId);
        
        if ($status) {
            $builder->where('project_collaborations.status', $status);
        }
        
        return $builder->orderBy('project_collaborations.assigned_at', 'DESC')->findAll();
    }

    /**
     * 添加协同部门
     * 
     * @param int $projectId 项目ID
     * @param int $departmentId 部门ID
     * @param string $collaborationContent 协作内容
     * @return int|false 协同关系ID或false
     */
    public function addCollaboration(int $projectId, int $departmentId, string $collaborationContent = ''): int|false
    {
        // 检查是否已存在
        $existing = $this->where('project_id', $projectId)
                        ->where('department_id', $departmentId)
                        ->first();
        
        if ($existing) {
            throw new \RuntimeException('该部门已参与此项目协同');
        }
        
        $data = [
            'project_id' => $projectId,
            'department_id' => $departmentId,
            'collaboration_content' => $collaborationContent,
            'status' => 'pending',
            'assigned_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->insert($data);
    }

    /**
     * 激活协同关系
     * 
     * @param int $collaborationId 协同关系ID
     * @return bool
     */
    public function activateCollaboration(int $collaborationId): bool
    {
        return $this->update($collaborationId, [
            'status' => 'active',
            'assigned_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 完成协同关系
     * 
     * @param int $collaborationId 协同关系ID
     * @return bool
     */
    public function completeCollaboration(int $collaborationId): bool
    {
        return $this->update($collaborationId, [
            'status' => 'completed',
            'completed_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 取消协同关系
     * 
     * @param int $collaborationId 协同关系ID
     * @param string $reason 取消原因
     * @return bool
     */
    public function cancelCollaboration(int $collaborationId, string $reason = ''): bool
    {
        $result = $this->update($collaborationId, ['status' => 'cancelled']);
        
        if ($result && $reason) {
            // 记录取消原因到日志
            $collaboration = $this->find($collaborationId);
            if ($collaboration) {
                $logModel = new ProjectLogModel();
                $logModel->logAction(
                    $collaboration['project_id'],
                    null,
                    'collaboration_cancelled',
                    "协同部门 {$collaboration['department_id']} 已取消",
                    session('user_id'),
                    ['reason' => $reason]
                );
            }
        }
        
        return $result;
    }

    /**
     * 批量添加协同部门
     * 
     * @param int $projectId 项目ID
     * @param array $departmentData 部门数据 [['department_id' => 1, 'content' => ''], ...]
     * @return bool
     */
    public function batchAddCollaborations(int $projectId, array $departmentData): bool
    {
        $this->db->transStart();
        
        foreach ($departmentData as $data) {
            $this->addCollaboration(
                $projectId,
                $data['department_id'],
                $data['collaboration_content'] ?? ''
            );
        }
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }

    /**
     * 移除协同部门
     * 
     * @param int $projectId 项目ID
     * @param int $departmentId 部门ID
     * @return bool
     */
    public function removeCollaboration(int $projectId, int $departmentId): bool
    {
        // 检查是否有正在进行的协同任务
        $nodeModel = new ProjectNodeModel();
        $pendingTasks = $nodeModel->where('department_id', $departmentId)
                                 ->whereIn('status', ['pending', 'running'])
                                 ->join('project_workflows', 'project_workflows.id = project_nodes.project_workflow_id')
                                 ->where('project_workflows.project_id', $projectId)
                                 ->countAllResults();
        
        if ($pendingTasks > 0) {
            throw new \RuntimeException('该部门还有未完成的协同任务，无法移除');
        }
        
        return $this->where('project_id', $projectId)
                   ->where('department_id', $departmentId)
                   ->delete();
    }

    /**
     * 获取协同统计信息
     * 
     * @param array $filters 筛选条件
     * @return array
     */
    public function getCollaborationStatistics(array $filters = []): array
    {
        $builder = $this->select('status, COUNT(*) as count')
                       ->groupBy('status');
        
        // 应用筛选条件
        if (!empty($filters['project_id'])) {
            $builder->where('project_id', $filters['project_id']);
        }
        
        if (!empty($filters['department_id'])) {
            $builder->where('department_id', $filters['department_id']);
        }
        
        if (!empty($filters['date_range'])) {
            $builder->where('created_at >=', $filters['date_range']['start'])
                   ->where('created_at <=', $filters['date_range']['end']);
        }
        
        $stats = $builder->findAll();
        
        $result = [
            'total' => 0,
            'pending' => 0,
            'active' => 0,
            'completed' => 0,
            'cancelled' => 0
        ];
        
        foreach ($stats as $stat) {
            $result[$stat['status']] = (int)$stat['count'];
            $result['total'] += (int)$stat['count'];
        }
        
        return $result;
    }

    /**
     * 获取部门协同效率统计
     * 
     * @param int $departmentId 部门ID
     * @param array $filters 筛选条件
     * @return array
     */
    public function getDepartmentEfficiencyStats(int $departmentId, array $filters = []): array
    {
        $builder = $this->select('AVG(TIMESTAMPDIFF(HOUR, assigned_at, completed_at)) as avg_duration, COUNT(*) as total_count')
                       ->where('department_id', $departmentId)
                       ->where('status', 'completed')
                       ->where('assigned_at IS NOT NULL')
                       ->where('completed_at IS NOT NULL');
        
        if (!empty($filters['date_range'])) {
            $builder->where('assigned_at >=', $filters['date_range']['start'])
                   ->where('assigned_at <=', $filters['date_range']['end']);
        }
        
        $result = $builder->first();
        
        return [
            'average_duration_hours' => $result ? round((float)$result['avg_duration'], 2) : 0,
            'completed_count' => $result ? (int)$result['total_count'] : 0
        ];
    }

    /**
     * 获取项目协同进度
     * 
     * @param int $projectId 项目ID
     * @return array
     */
    public function getProjectCollaborationProgress(int $projectId): array
    {
        $collaborations = $this->getProjectCollaborations($projectId);
        
        $progress = [
            'total_departments' => count($collaborations),
            'completed_departments' => 0,
            'active_departments' => 0,
            'pending_departments' => 0,
            'progress_percentage' => 0,
            'departments' => []
        ];
        
        foreach ($collaborations as $collaboration) {
            switch ($collaboration['status']) {
                case 'completed':
                    $progress['completed_departments']++;
                    break;
                case 'active':
                    $progress['active_departments']++;
                    break;
                case 'pending':
                    $progress['pending_departments']++;
                    break;
            }
            
            $progress['departments'][] = [
                'id' => $collaboration['department_id'],
                'name' => $collaboration['department_name'],
                'status' => $collaboration['status'],
                'assigned_at' => $collaboration['assigned_at'],
                'completed_at' => $collaboration['completed_at']
            ];
        }
        
        if ($progress['total_departments'] > 0) {
            $progress['progress_percentage'] = round(
                ($progress['completed_departments'] / $progress['total_departments']) * 100,
                2
            );
        }
        
        return $progress;
    }

    /**
     * 检查部门是否可以参与项目协同
     * 
     * @param int $projectId 项目ID
     * @param int $departmentId 部门ID
     * @return bool
     */
    public function canDepartmentCollaborate(int $projectId, int $departmentId): bool
    {
        // 获取项目信息
        $projectModel = new ProjectModel();
        $project = $projectModel->find($projectId);
        
        if (!$project) {
            return false;
        }
        
        // 检查部门是否支持该产品线
        $departmentModel = new DepartmentModel();
        if (!$departmentModel->supportsProductLine($departmentId, $project['product_line'])) {
            return false;
        }
        
        // 检查是否已参与协同
        $existing = $this->where('project_id', $projectId)
                        ->where('department_id', $departmentId)
                        ->first();
        
        return !$existing;
    }

    /**
     * 获取状态标签
     * 
     * @param string $status 状态
     * @return string
     */
    public function getStatusLabel(string $status): string
    {
        $labels = [
            'pending' => '待分配',
            'active' => '进行中',
            'completed' => '已完成',
            'cancelled' => '已取消'
        ];
        
        return $labels[$status] ?? $status;
    }
}
