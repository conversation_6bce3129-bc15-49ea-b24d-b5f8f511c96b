<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * 节点定义模型
 * 管理工作流中的节点定义
 */
class NodeDefinitionModel extends Model
{
    protected $table = 'node_definitions';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'workflow_id', 'node_code', 'node_name', 'node_type', 'sequence',
        'assignee_type', 'action_required', 'can_reject', 'reject_to_node',
        'auto_assign', 'is_parallel', 'conditions'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected $validationRules = [
        'workflow_id' => 'required|integer',
        'node_code' => 'required|max_length[50]',
        'node_name' => 'required|max_length[100]',
        'node_type' => 'required|in_list[main,collaborative]',
        'sequence' => 'required|integer',
        'assignee_type' => 'required|in_list[sales,main_manager,main_executor,collab_manager,collab_executor,collab_analyst,business]',
        'action_required' => 'required',
        'can_reject' => 'in_list[0,1]',
        'auto_assign' => 'in_list[0,1]',
        'is_parallel' => 'in_list[0,1]'
    ];
    
    protected $validationMessages = [
        'workflow_id' => [
            'required' => '工作流ID不能为空',
            'integer' => '工作流ID必须是整数'
        ],
        'node_code' => [
            'required' => '节点编码不能为空',
            'max_length' => '节点编码长度不能超过50个字符'
        ],
        'node_name' => [
            'required' => '节点名称不能为空',
            'max_length' => '节点名称长度不能超过100个字符'
        ]
    ];

    /**
     * 获取工作流的所有节点定义
     * 
     * @param int $workflowId 工作流ID
     * @param string $nodeType 节点类型（可选）
     * @return array
     */
    public function getWorkflowNodes(int $workflowId, string $nodeType = null): array
    {
        $builder = $this->where('workflow_id', $workflowId);
        
        if ($nodeType) {
            $builder->where('node_type', $nodeType);
        }
        
        return $builder->orderBy('sequence', 'ASC')->findAll();
    }

    /**
     * 根据节点编码获取节点定义
     * 
     * @param int $workflowId 工作流ID
     * @param string $nodeCode 节点编码
     * @return array|null
     */
    public function getByNodeCode(int $workflowId, string $nodeCode): ?array
    {
        return $this->where('workflow_id', $workflowId)
                   ->where('node_code', $nodeCode)
                   ->first();
    }

    /**
     * 获取第一个节点定义
     * 
     * @param int $workflowId 工作流ID
     * @return array|null
     */
    public function getFirstNode(int $workflowId): ?array
    {
        return $this->where('workflow_id', $workflowId)
                   ->orderBy('sequence', 'ASC')
                   ->first();
    }

    /**
     * 获取下一个节点定义
     * 
     * @param int $workflowId 工作流ID
     * @param int $currentSequence 当前节点序号
     * @param array $conditions 条件（可选）
     * @return array|null
     */
    public function getNextNode(int $workflowId, int $currentSequence, array $conditions = []): ?array
    {
        $builder = $this->where('workflow_id', $workflowId)
                       ->where('sequence >', $currentSequence)
                       ->orderBy('sequence', 'ASC');
        
        $nodes = $builder->findAll();
        
        // 如果没有条件，返回下一个节点
        if (empty($conditions)) {
            return $nodes[0] ?? null;
        }
        
        // 根据条件筛选节点
        foreach ($nodes as $node) {
            if ($this->matchConditions($node, $conditions)) {
                return $node;
            }
        }
        
        return null;
    }

    /**
     * 获取并行节点（协同节点）
     * 
     * @param int $workflowId 工作流ID
     * @param int $sequence 序号
     * @return array
     */
    public function getParallelNodes(int $workflowId, int $sequence): array
    {
        return $this->where('workflow_id', $workflowId)
                   ->where('sequence', $sequence)
                   ->where('is_parallel', 1)
                   ->where('node_type', 'collaborative')
                   ->findAll();
    }

    /**
     * 创建节点定义
     * 
     * @param array $data 节点数据
     * @return int|false 节点ID或false
     */
    public function createNode(array $data)
    {
        // 处理JSON字段
        if (isset($data['action_required']) && is_array($data['action_required'])) {
            $data['action_required'] = json_encode($data['action_required']);
        }
        
        if (isset($data['conditions']) && is_array($data['conditions'])) {
            $data['conditions'] = json_encode($data['conditions']);
        }
        
        // 检查节点编码唯一性
        $existing = $this->where('workflow_id', $data['workflow_id'])
                        ->where('node_code', $data['node_code'])
                        ->first();
        
        if ($existing) {
            throw new \InvalidArgumentException('节点编码在同一工作流中必须唯一');
        }
        
        return $this->insert($data);
    }

    /**
     * 更新节点定义
     * 
     * @param int $nodeId 节点ID
     * @param array $data 更新数据
     * @return bool
     */
    public function updateNode(int $nodeId, array $data): bool
    {
        // 处理JSON字段
        if (isset($data['action_required']) && is_array($data['action_required'])) {
            $data['action_required'] = json_encode($data['action_required']);
        }
        
        if (isset($data['conditions']) && is_array($data['conditions'])) {
            $data['conditions'] = json_encode($data['conditions']);
        }
        
        return $this->update($nodeId, $data);
    }

    /**
     * 删除节点定义
     * 
     * @param int $nodeId 节点ID
     * @return bool
     */
    public function deleteNode(int $nodeId): bool
    {
        // 检查是否有项目节点实例使用此定义
        $projectNodeModel = new ProjectNodeModel();
        $usageCount = $projectNodeModel->where('node_definition_id', $nodeId)
                                      ->countAllResults();
        
        if ($usageCount > 0) {
            throw new \RuntimeException('无法删除正在使用的节点定义');
        }
        
        return $this->delete($nodeId);
    }

    /**
     * 复制节点定义到新工作流
     * 
     * @param int $sourceWorkflowId 源工作流ID
     * @param int $targetWorkflowId 目标工作流ID
     * @return bool
     */
    public function duplicateNodes(int $sourceWorkflowId, int $targetWorkflowId): bool
    {
        $sourceNodes = $this->where('workflow_id', $sourceWorkflowId)
                           ->orderBy('sequence', 'ASC')
                           ->findAll();
        
        foreach ($sourceNodes as $node) {
            unset($node['id'], $node['created_at'], $node['updated_at']);
            $node['workflow_id'] = $targetWorkflowId;
            
            $this->insert($node);
        }
        
        return true;
    }

    /**
     * 重新排序节点
     * 
     * @param int $workflowId 工作流ID
     * @param array $nodeSequences 节点ID和序号的映射 [nodeId => sequence]
     * @return bool
     */
    public function reorderNodes(int $workflowId, array $nodeSequences): bool
    {
        $this->db->transStart();
        
        foreach ($nodeSequences as $nodeId => $sequence) {
            $this->where('id', $nodeId)
                 ->where('workflow_id', $workflowId)
                 ->set('sequence', $sequence)
                 ->update();
        }
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }

    /**
     * 获取节点的处理人类型说明
     * 
     * @param string $assigneeType 处理人类型
     * @return string
     */
    public function getAssigneeTypeLabel(string $assigneeType): string
    {
        $labels = [
            'sales' => '销售人员',
            'main_manager' => '主部门负责人',
            'main_executor' => '主部门执行人员',
            'collab_manager' => '协同部门负责人',
            'collab_executor' => '协同部门执行人员',
            'collab_analyst' => '协同部门数据分析师',
            'business' => '商务负责人'
        ];
        
        return $labels[$assigneeType] ?? $assigneeType;
    }

    /**
     * 获取节点类型说明
     * 
     * @param string $nodeType 节点类型
     * @return string
     */
    public function getNodeTypeLabel(string $nodeType): string
    {
        $labels = [
            'main' => '主节点',
            'collaborative' => '协同节点'
        ];
        
        return $labels[$nodeType] ?? $nodeType;
    }

    /**
     * 检查条件是否匹配
     * 
     * @param array $node 节点定义
     * @param array $conditions 条件
     * @return bool
     */
    private function matchConditions(array $node, array $conditions): bool
    {
        if (empty($node['conditions'])) {
            return true;
        }
        
        $nodeConditions = is_string($node['conditions']) 
            ? json_decode($node['conditions'], true) 
            : $node['conditions'];
        
        if (!$nodeConditions) {
            return true;
        }
        
        foreach ($nodeConditions as $key => $value) {
            if (!isset($conditions[$key]) || $conditions[$key] !== $value) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取节点的操作要求
     * 
     * @param int $nodeId 节点ID
     * @return array
     */
    public function getActionRequired(int $nodeId): array
    {
        $node = $this->find($nodeId);
        if (!$node) {
            return [];
        }
        
        return is_string($node['action_required']) 
            ? json_decode($node['action_required'], true) 
            : $node['action_required'];
    }

    /**
     * 验证节点配置
     * 
     * @param array $nodeData 节点数据
     * @return array 验证结果
     */
    public function validateNodeConfig(array $nodeData): array
    {
        $errors = [];
        
        // 验证必填字段
        $requiredFields = ['workflow_id', 'node_code', 'node_name', 'sequence', 'assignee_type'];
        foreach ($requiredFields as $field) {
            if (empty($nodeData[$field])) {
                $errors[] = "字段 {$field} 不能为空";
            }
        }
        
        // 验证JSON字段
        if (isset($nodeData['action_required'])) {
            if (is_string($nodeData['action_required'])) {
                $decoded = json_decode($nodeData['action_required'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $errors[] = 'action_required 必须是有效的JSON格式';
                }
            }
        }
        
        if (isset($nodeData['conditions']) && !empty($nodeData['conditions'])) {
            if (is_string($nodeData['conditions'])) {
                $decoded = json_decode($nodeData['conditions'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $errors[] = 'conditions 必须是有效的JSON格式';
                }
            }
        }
        
        // 验证驳回配置
        if (!empty($nodeData['can_reject']) && empty($nodeData['reject_to_node'])) {
            $errors[] = '启用驳回功能时必须指定驳回目标节点';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
