<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * 项目工作流模型
 * 管理项目的工作流执行实例
 */
class ProjectWorkflowModel extends Model
{
    protected $table = 'project_workflows';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'project_id', 'workflow_definition_id', 'current_node_code',
        'status', 'started_at', 'completed_at'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected $validationRules = [
        'project_id' => 'required|integer',
        'workflow_definition_id' => 'required|integer',
        'status' => 'in_list[pending,running,completed,cancelled,rejected]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => '项目ID不能为空',
            'integer' => '项目ID必须是整数'
        ],
        'workflow_definition_id' => [
            'required' => '工作流定义ID不能为空',
            'integer' => '工作流定义ID必须是整数'
        ]
    ];

    /**
     * 根据项目ID获取工作流实例
     * 
     * @param int $projectId 项目ID
     * @return array|null
     */
    public function getByProjectId(int $projectId): ?array
    {
        return $this->where('project_id', $projectId)->first();
    }

    /**
     * 创建项目工作流实例
     * 
     * @param int $projectId 项目ID
     * @param string $productLine 产品线
     * @return int|false 工作流实例ID或false
     */
    public function createProjectWorkflow(int $projectId, string $productLine)
    {
        // 检查是否已存在工作流实例
        $existing = $this->getByProjectId($projectId);
        if ($existing) {
            throw new \RuntimeException('项目已存在工作流实例');
        }
        
        // 获取对应产品线的工作流定义
        $workflowModel = new WorkflowDefinitionModel();
        $workflow = $workflowModel->getActiveByProductLine($productLine);
        
        if (!$workflow) {
            throw new \RuntimeException("产品线 {$productLine} 没有可用的工作流定义");
        }
        
        // 创建工作流实例
        $data = [
            'project_id' => $projectId,
            'workflow_definition_id' => $workflow['id'],
            'status' => 'pending'
        ];
        
        $workflowId = $this->insert($data);
        
        if ($workflowId) {
            // 创建第一个节点实例
            $nodeModel = new ProjectNodeModel();
            $success = $nodeModel->createFirstNode($workflowId, $workflow['id']);
            
            if ($success) {
                // 更新工作流状态为运行中
                $this->startWorkflow($workflowId);
                return $workflowId;
            }
        }
        
        return false;
    }

    /**
     * 启动工作流
     * 
     * @param int $workflowId 工作流实例ID
     * @return bool
     */
    public function startWorkflow(int $workflowId): bool
    {
        $workflow = $this->find($workflowId);
        if (!$workflow) {
            return false;
        }
        
        // 获取第一个节点
        $nodeDefinitionModel = new NodeDefinitionModel();
        $firstNode = $nodeDefinitionModel->getFirstNode($workflow['workflow_definition_id']);
        
        if (!$firstNode) {
            return false;
        }
        
        return $this->update($workflowId, [
            'status' => 'running',
            'current_node_code' => $firstNode['node_code'],
            'started_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 流转到下一个节点
     * 
     * @param int $workflowId 工作流实例ID
     * @param string $nextNodeCode 下一个节点编码
     * @return bool
     */
    public function moveToNextNode(int $workflowId, string $nextNodeCode): bool
    {
        return $this->update($workflowId, [
            'current_node_code' => $nextNodeCode,
            'status' => 'running'
        ]);
    }

    /**
     * 完成工作流
     * 
     * @param int $workflowId 工作流实例ID
     * @return bool
     */
    public function completeWorkflow(int $workflowId): bool
    {
        return $this->update($workflowId, [
            'status' => 'completed',
            'completed_at' => date('Y-m-d H:i:s'),
            'current_node_code' => null
        ]);
    }

    /**
     * 取消工作流
     * 
     * @param int $workflowId 工作流实例ID
     * @param string $reason 取消原因
     * @return bool
     */
    public function cancelWorkflow(int $workflowId, string $reason = ''): bool
    {
        $result = $this->update($workflowId, [
            'status' => 'cancelled',
            'completed_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($result && $reason) {
            // 记录取消原因到日志
            $logModel = new ProjectLogModel();
            $workflow = $this->find($workflowId);
            if ($workflow) {
                $logModel->logAction(
                    $workflow['project_id'],
                    null,
                    'workflow_cancelled',
                    '工作流已取消',
                    null,
                    ['reason' => $reason]
                );
            }
        }
        
        return $result;
    }

    /**
     * 驳回工作流到指定节点
     * 
     * @param int $workflowId 工作流实例ID
     * @param string $targetNodeCode 目标节点编码
     * @param string $reason 驳回原因
     * @return bool
     */
    public function rejectToNode(int $workflowId, string $targetNodeCode, string $reason = ''): bool
    {
        $workflow = $this->find($workflowId);
        if (!$workflow) {
            return false;
        }
        
        // 重置目标节点及其后续节点的状态
        $nodeModel = new ProjectNodeModel();
        $nodeModel->resetNodesFromTarget($workflowId, $targetNodeCode);
        
        // 更新工作流当前节点
        $result = $this->update($workflowId, [
            'current_node_code' => $targetNodeCode,
            'status' => 'running'
        ]);
        
        if ($result && $reason) {
            // 记录驳回原因到日志
            $logModel = new ProjectLogModel();
            $logModel->logAction(
                $workflow['project_id'],
                null,
                'workflow_rejected',
                "工作流已驳回到节点: {$targetNodeCode}",
                null,
                ['target_node' => $targetNodeCode, 'reason' => $reason]
            );
        }
        
        return $result;
    }

    /**
     * 获取工作流执行进度
     * 
     * @param int $workflowId 工作流实例ID
     * @return array
     */
    public function getProgress(int $workflowId): array
    {
        $workflow = $this->find($workflowId);
        if (!$workflow) {
            return [];
        }
        
        // 获取所有节点定义
        $nodeDefinitionModel = new NodeDefinitionModel();
        $allNodes = $nodeDefinitionModel->getWorkflowNodes($workflow['workflow_definition_id']);
        
        // 获取已完成的节点实例
        $nodeModel = new ProjectNodeModel();
        $completedNodes = $nodeModel->where('project_workflow_id', $workflowId)
                                   ->where('status', 'completed')
                                   ->findAll();
        
        $totalNodes = count($allNodes);
        $completedCount = count($completedNodes);
        
        return [
            'total_nodes' => $totalNodes,
            'completed_nodes' => $completedCount,
            'progress_percentage' => $totalNodes > 0 ? round(($completedCount / $totalNodes) * 100, 2) : 0,
            'current_node_code' => $workflow['current_node_code'],
            'status' => $workflow['status'],
            'started_at' => $workflow['started_at'],
            'completed_at' => $workflow['completed_at']
        ];
    }

    /**
     * 获取工作流执行历史
     * 
     * @param int $workflowId 工作流实例ID
     * @return array
     */
    public function getExecutionHistory(int $workflowId): array
    {
        $nodeModel = new ProjectNodeModel();
        $nodes = $nodeModel->select('project_nodes.*, node_definitions.node_name, node_definitions.node_type, users.real_name as assignee_name')
                          ->join('node_definitions', 'node_definitions.id = project_nodes.node_definition_id')
                          ->join('users', 'users.id = project_nodes.assignee_id', 'left')
                          ->where('project_nodes.project_workflow_id', $workflowId)
                          ->orderBy('project_nodes.created_at', 'ASC')
                          ->findAll();
        
        return $nodes;
    }

    /**
     * 获取工作流统计信息
     * 
     * @param array $filters 筛选条件
     * @return array
     */
    public function getStatistics(array $filters = []): array
    {
        $builder = $this->select('status, COUNT(*) as count')
                       ->groupBy('status');
        
        // 应用筛选条件
        if (!empty($filters['product_line'])) {
            $builder->join('workflow_definitions', 'workflow_definitions.id = project_workflows.workflow_definition_id')
                   ->where('workflow_definitions.product_line', $filters['product_line']);
        }
        
        if (!empty($filters['date_range'])) {
            $builder->where('project_workflows.created_at >=', $filters['date_range']['start'])
                   ->where('project_workflows.created_at <=', $filters['date_range']['end']);
        }
        
        $statusStats = $builder->findAll();
        
        $stats = [
            'total' => 0,
            'pending' => 0,
            'running' => 0,
            'completed' => 0,
            'cancelled' => 0,
            'rejected' => 0
        ];
        
        foreach ($statusStats as $stat) {
            $stats[$stat['status']] = (int)$stat['count'];
            $stats['total'] += (int)$stat['count'];
        }
        
        return $stats;
    }

    /**
     * 获取超时的工作流
     * 
     * @param int $timeoutHours 超时小时数
     * @return array
     */
    public function getTimeoutWorkflows(int $timeoutHours = 24): array
    {
        $timeoutDate = date('Y-m-d H:i:s', strtotime("-{$timeoutHours} hours"));
        
        return $this->select('project_workflows.*, projects.title as project_title, projects.project_no')
                   ->join('projects', 'projects.id = project_workflows.project_id')
                   ->where('project_workflows.status', 'running')
                   ->where('project_workflows.updated_at <', $timeoutDate)
                   ->findAll();
    }

    /**
     * 检查工作流是否可以操作
     * 
     * @param int $workflowId 工作流实例ID
     * @param string $operation 操作类型
     * @return bool
     */
    public function canOperate(int $workflowId, string $operation): bool
    {
        $workflow = $this->find($workflowId);
        if (!$workflow) {
            return false;
        }
        
        switch ($operation) {
            case 'start':
                return $workflow['status'] === 'pending';
            case 'complete':
            case 'cancel':
            case 'reject':
                return in_array($workflow['status'], ['pending', 'running']);
            case 'restart':
                return in_array($workflow['status'], ['completed', 'cancelled', 'rejected']);
            default:
                return false;
        }
    }

    /**
     * 重启工作流
     * 
     * @param int $workflowId 工作流实例ID
     * @return bool
     */
    public function restartWorkflow(int $workflowId): bool
    {
        if (!$this->canOperate($workflowId, 'restart')) {
            return false;
        }
        
        $workflow = $this->find($workflowId);
        
        // 重置所有节点状态
        $nodeModel = new ProjectNodeModel();
        $nodeModel->where('project_workflow_id', $workflowId)
                 ->set([
                     'status' => 'pending',
                     'assignee_id' => null,
                     'started_at' => null,
                     'completed_at' => null,
                     'result_data' => null,
                     'reject_reason' => null
                 ])
                 ->update();
        
        // 重置工作流状态
        return $this->update($workflowId, [
            'status' => 'pending',
            'current_node_code' => null,
            'started_at' => null,
            'completed_at' => null
        ]);
    }
}
