<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * 项目节点模型
 * 管理项目节点的执行实例
 */
class ProjectNodeModel extends Model
{
    protected $table = 'project_nodes';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'project_workflow_id', 'node_definition_id', 'node_code',
        'assignee_id', 'department_id', 'status', 'started_at',
        'completed_at', 'due_date', 'result_data', 'reject_reason'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected $validationRules = [
        'project_workflow_id' => 'required|integer',
        'node_definition_id' => 'required|integer',
        'node_code' => 'required|max_length[50]',
        'status' => 'in_list[pending,running,completed,rejected,skipped]'
    ];

    /**
     * 创建第一个节点实例
     * 
     * @param int $workflowId 工作流实例ID
     * @param int $workflowDefinitionId 工作流定义ID
     * @return bool
     */
    public function createFirstNode(int $workflowId, int $workflowDefinitionId): bool
    {
        $nodeDefinitionModel = new NodeDefinitionModel();
        $firstNode = $nodeDefinitionModel->getFirstNode($workflowDefinitionId);
        
        if (!$firstNode) {
            return false;
        }
        
        $data = [
            'project_workflow_id' => $workflowId,
            'node_definition_id' => $firstNode['id'],
            'node_code' => $firstNode['node_code'],
            'status' => 'pending'
        ];
        
        // 如果是自动分配，尝试分配处理人
        if ($firstNode['auto_assign']) {
            $assigneeId = $this->autoAssignUser($workflowId, $firstNode);
            if ($assigneeId) {
                $data['assignee_id'] = $assigneeId;
                $data['status'] = 'running';
                $data['started_at'] = date('Y-m-d H:i:s');
            }
        }
        
        return $this->insert($data) !== false;
    }

    /**
     * 创建下一个节点实例
     * 
     * @param int $workflowId 工作流实例ID
     * @param array $nodeDefinition 节点定义
     * @param int|null $departmentId 部门ID（协同节点使用）
     * @return int|false 节点实例ID或false
     */
    public function createNextNode(int $workflowId, array $nodeDefinition, int $departmentId = null)
    {
        $data = [
            'project_workflow_id' => $workflowId,
            'node_definition_id' => $nodeDefinition['id'],
            'node_code' => $nodeDefinition['node_code'],
            'status' => 'pending',
            'department_id' => $departmentId
        ];
        
        // 如果是自动分配，尝试分配处理人
        if ($nodeDefinition['auto_assign']) {
            $assigneeId = $this->autoAssignUser($workflowId, $nodeDefinition, $departmentId);
            if ($assigneeId) {
                $data['assignee_id'] = $assigneeId;
                $data['status'] = 'running';
                $data['started_at'] = date('Y-m-d H:i:s');
            }
        }
        
        return $this->insert($data);
    }

    /**
     * 完成节点处理
     * 
     * @param int $nodeId 节点ID
     * @param array $resultData 处理结果数据
     * @param int $assigneeId 处理人ID
     * @return bool
     */
    public function completeNode(int $nodeId, array $resultData, int $assigneeId): bool
    {
        $data = [
            'status' => 'completed',
            'completed_at' => date('Y-m-d H:i:s'),
            'result_data' => json_encode($resultData),
            'assignee_id' => $assigneeId
        ];
        
        $result = $this->update($nodeId, $data);
        
        if ($result) {
            // 记录操作日志
            $node = $this->find($nodeId);
            if ($node) {
                $logModel = new ProjectLogModel();
                $workflowModel = new ProjectWorkflowModel();
                $workflow = $workflowModel->find($node['project_workflow_id']);
                
                if ($workflow) {
                    $logModel->logAction(
                        $workflow['project_id'],
                        $nodeId,
                        'node_completed',
                        "节点 {$node['node_code']} 已完成",
                        $assigneeId,
                        $resultData
                    );
                }
            }
        }
        
        return $result;
    }

    /**
     * 驳回节点
     * 
     * @param int $nodeId 节点ID
     * @param string $rejectReason 驳回原因
     * @param int $assigneeId 处理人ID
     * @return bool
     */
    public function rejectNode(int $nodeId, string $rejectReason, int $assigneeId): bool
    {
        $data = [
            'status' => 'rejected',
            'completed_at' => date('Y-m-d H:i:s'),
            'reject_reason' => $rejectReason,
            'assignee_id' => $assigneeId
        ];
        
        $result = $this->update($nodeId, $data);
        
        if ($result) {
            // 记录操作日志
            $node = $this->find($nodeId);
            if ($node) {
                $logModel = new ProjectLogModel();
                $workflowModel = new ProjectWorkflowModel();
                $workflow = $workflowModel->find($node['project_workflow_id']);
                
                if ($workflow) {
                    $logModel->logAction(
                        $workflow['project_id'],
                        $nodeId,
                        'node_rejected',
                        "节点 {$node['node_code']} 已驳回",
                        $assigneeId,
                        ['reject_reason' => $rejectReason]
                    );
                }
            }
        }
        
        return $result;
    }

    /**
     * 分配节点处理人
     * 
     * @param int $nodeId 节点ID
     * @param int $assigneeId 处理人ID
     * @return bool
     */
    public function assignNode(int $nodeId, int $assigneeId): bool
    {
        $node = $this->find($nodeId);
        if (!$node || $node['status'] !== 'pending') {
            return false;
        }
        
        return $this->update($nodeId, [
            'assignee_id' => $assigneeId,
            'status' => 'running',
            'started_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 获取用户待处理任务
     * 
     * @param int $userId 用户ID
     * @param array $filters 筛选条件
     * @return array
     */
    public function getPendingTasks(int $userId, array $filters = []): array
    {
        $builder = $this->select('project_nodes.*, node_definitions.node_name, node_definitions.action_required, projects.title as project_title, projects.project_no')
                       ->join('node_definitions', 'node_definitions.id = project_nodes.node_definition_id')
                       ->join('project_workflows', 'project_workflows.id = project_nodes.project_workflow_id')
                       ->join('projects', 'projects.id = project_workflows.project_id')
                       ->where('project_nodes.assignee_id', $userId)
                       ->whereIn('project_nodes.status', ['pending', 'running']);
        
        // 应用筛选条件
        if (!empty($filters['product_line'])) {
            $builder->where('projects.product_line', $filters['product_line']);
        }
        
        if (!empty($filters['department_id'])) {
            $builder->where('project_nodes.department_id', $filters['department_id']);
        }
        
        if (!empty($filters['node_type'])) {
            $builder->where('node_definitions.node_type', $filters['node_type']);
        }
        
        return $builder->orderBy('project_nodes.created_at', 'ASC')->findAll();
    }

    /**
     * 获取部门待处理任务
     * 
     * @param int $departmentId 部门ID
     * @param array $filters 筛选条件
     * @return array
     */
    public function getDepartmentTasks(int $departmentId, array $filters = []): array
    {
        $builder = $this->select('project_nodes.*, node_definitions.node_name, node_definitions.assignee_type, projects.title as project_title, projects.project_no, users.real_name as assignee_name')
                       ->join('node_definitions', 'node_definitions.id = project_nodes.node_definition_id')
                       ->join('project_workflows', 'project_workflows.id = project_nodes.project_workflow_id')
                       ->join('projects', 'projects.id = project_workflows.project_id')
                       ->join('users', 'users.id = project_nodes.assignee_id', 'left')
                       ->where('project_nodes.department_id', $departmentId)
                       ->whereIn('project_nodes.status', ['pending', 'running']);
        
        // 应用筛选条件
        if (!empty($filters['status'])) {
            $builder->where('project_nodes.status', $filters['status']);
        }
        
        if (!empty($filters['assignee_type'])) {
            $builder->where('node_definitions.assignee_type', $filters['assignee_type']);
        }
        
        return $builder->orderBy('project_nodes.created_at', 'ASC')->findAll();
    }

    /**
     * 重置节点状态（从指定节点开始）
     * 
     * @param int $workflowId 工作流实例ID
     * @param string $fromNodeCode 起始节点编码
     * @return bool
     */
    public function resetNodesFromTarget(int $workflowId, string $fromNodeCode): bool
    {
        // 获取目标节点的序号
        $nodeDefinitionModel = new NodeDefinitionModel();
        $workflowModel = new ProjectWorkflowModel();
        $workflow = $workflowModel->find($workflowId);
        
        if (!$workflow) {
            return false;
        }
        
        $targetNode = $nodeDefinitionModel->getByNodeCode($workflow['workflow_definition_id'], $fromNodeCode);
        if (!$targetNode) {
            return false;
        }
        
        // 获取需要重置的节点（序号大于等于目标节点的）
        $nodesToReset = $nodeDefinitionModel->where('workflow_id', $workflow['workflow_definition_id'])
                                           ->where('sequence >=', $targetNode['sequence'])
                                           ->findAll();
        
        $nodeIds = array_column($nodesToReset, 'id');
        
        if (!empty($nodeIds)) {
            // 重置节点状态
            $this->whereIn('node_definition_id', $nodeIds)
                 ->where('project_workflow_id', $workflowId)
                 ->set([
                     'status' => 'pending',
                     'assignee_id' => null,
                     'started_at' => null,
                     'completed_at' => null,
                     'result_data' => null,
                     'reject_reason' => null
                 ])
                 ->update();
        }
        
        return true;
    }

    /**
     * 自动分配用户
     * 
     * @param int $workflowId 工作流实例ID
     * @param array $nodeDefinition 节点定义
     * @param int|null $departmentId 部门ID
     * @return int|null 用户ID
     */
    private function autoAssignUser(int $workflowId, array $nodeDefinition, int $departmentId = null): ?int
    {
        $workflowModel = new ProjectWorkflowModel();
        $projectModel = new ProjectModel();
        $userDepartmentModel = new UserDepartmentModel();
        
        $workflow = $workflowModel->find($workflowId);
        if (!$workflow) {
            return null;
        }
        
        $project = $projectModel->find($workflow['project_id']);
        if (!$project) {
            return null;
        }
        
        switch ($nodeDefinition['assignee_type']) {
            case 'sales':
                return $project['sales_user_id'];
                
            case 'business':
                return $project['business_user_id'];
                
            case 'main_manager':
                if ($project['main_department_id']) {
                    $departmentModel = new DepartmentModel();
                    $department = $departmentModel->find($project['main_department_id']);
                    return $department ? $department['manager_id'] : null;
                }
                break;
                
            case 'collab_manager':
                if ($departmentId) {
                    $departmentModel = new DepartmentModel();
                    $department = $departmentModel->find($departmentId);
                    return $department ? $department['manager_id'] : null;
                }
                break;
                
            case 'main_executor':
            case 'collab_executor':
            case 'collab_analyst':
                // 这些角色需要手动分配，不能自动分配
                return null;
        }
        
        return null;
    }

    /**
     * 获取节点执行统计
     * 
     * @param array $filters 筛选条件
     * @return array
     */
    public function getExecutionStatistics(array $filters = []): array
    {
        $builder = $this->select('status, COUNT(*) as count, AVG(TIMESTAMPDIFF(HOUR, started_at, completed_at)) as avg_duration')
                       ->groupBy('status');
        
        // 应用筛选条件
        if (!empty($filters['date_range'])) {
            $builder->where('created_at >=', $filters['date_range']['start'])
                   ->where('created_at <=', $filters['date_range']['end']);
        }
        
        if (!empty($filters['node_type'])) {
            $builder->join('node_definitions', 'node_definitions.id = project_nodes.node_definition_id')
                   ->where('node_definitions.node_type', $filters['node_type']);
        }
        
        $stats = $builder->findAll();
        
        $result = [
            'total' => 0,
            'pending' => 0,
            'running' => 0,
            'completed' => 0,
            'rejected' => 0,
            'skipped' => 0,
            'average_duration' => 0
        ];
        
        $totalDuration = 0;
        $completedCount = 0;
        
        foreach ($stats as $stat) {
            $result[$stat['status']] = (int)$stat['count'];
            $result['total'] += (int)$stat['count'];
            
            if ($stat['status'] === 'completed' && $stat['avg_duration']) {
                $totalDuration += $stat['avg_duration'] * $stat['count'];
                $completedCount += $stat['count'];
            }
        }
        
        if ($completedCount > 0) {
            $result['average_duration'] = round($totalDuration / $completedCount, 2);
        }
        
        return $result;
    }

    /**
     * 获取超时的节点
     * 
     * @param int $timeoutHours 超时小时数
     * @return array
     */
    public function getTimeoutNodes(int $timeoutHours = 24): array
    {
        $timeoutDate = date('Y-m-d H:i:s', strtotime("-{$timeoutHours} hours"));
        
        return $this->select('project_nodes.*, node_definitions.node_name, projects.title as project_title, projects.project_no, users.real_name as assignee_name')
                   ->join('node_definitions', 'node_definitions.id = project_nodes.node_definition_id')
                   ->join('project_workflows', 'project_workflows.id = project_nodes.project_workflow_id')
                   ->join('projects', 'projects.id = project_workflows.project_id')
                   ->join('users', 'users.id = project_nodes.assignee_id', 'left')
                   ->where('project_nodes.status', 'running')
                   ->where('project_nodes.started_at <', $timeoutDate)
                   ->findAll();
    }
}
