<?php

namespace App\Services;

use App\Models\WorkflowDefinitionModel;
use App\Models\NodeDefinitionModel;
use App\Models\ProjectWorkflowModel;
use CodeIgniter\Database\Exceptions\DatabaseException;

/**
 * 工作流定义服务
 * 封装工作流定义相关的业务逻辑
 */
class WorkflowDefinitionService
{
    protected WorkflowDefinitionModel $workflowModel;
    protected NodeDefinitionModel $nodeModel;
    protected ProjectWorkflowModel $projectWorkflowModel;

    /**
     * 构造函数
     * 初始化模型
     */
    public function __construct()
    {
        $this->workflowModel = new WorkflowDefinitionModel();
        $this->nodeModel = new NodeDefinitionModel();
        $this->projectWorkflowModel = new ProjectWorkflowModel();
    }

    /**
     * 获取工作流列表
     * 
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array
     */
    public function getWorkflowList(array $filters = [], int $page = 1, int $perPage = 20): array
    {
        try {
            $builder = $this->workflowModel;

            // 应用筛选条件
            if (!empty($filters['product_line'])) {
                $builder = $builder->where('product_line', $filters['product_line']);
            }

            if (isset($filters['is_active']) && $filters['is_active'] !== '') {
                $builder = $builder->where('is_active', (int)$filters['is_active']);
            }

            if (!empty($filters['keyword'])) {
                $builder = $builder->groupStart()
                    ->like('name', $filters['keyword'])
                    ->orLike('code', $filters['keyword'])
                    ->orLike('description', $filters['keyword'])
                    ->groupEnd();
            }

            // 获取总数
            $total = $builder->countAllResults(false);

            // 分页查询
            $workflows = $builder->orderBy('created_at', 'DESC')
                ->paginate($perPage, 'default', $page);

            // 获取分页器
            $pager = $this->workflowModel->pager;

            // 为每个工作流添加统计信息
            foreach ($workflows as &$workflow) {
                $workflow['node_count'] = $this->nodeModel->where('workflow_id', $workflow['id'])->countAllResults();
                $workflow['project_count'] = $this->projectWorkflowModel->where('workflow_definition_id', $workflow['id'])->countAllResults();
            }

            return [
                'success' => true,
                'data' => $workflows,
                'total' => $total,
                'pager' => $pager
            ];

        } catch (\Exception $e) {
            log_message('error', '获取工作流列表失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '获取工作流列表失败',
                'data' => [],
                'total' => 0,
                'pager' => null
            ];
        }
    }

    /**
     * 获取工作流详情
     * 
     * @param int $id 工作流ID
     * @return array|null
     */
    public function getWorkflowDetail(int $id): ?array
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return null;
            }

            // 获取节点列表
            $nodes = $this->nodeModel->where('workflow_id', $id)
                ->orderBy('sequence', 'ASC')
                ->findAll();

            // 解析节点的JSON字段
            foreach ($nodes as &$node) {
                if ($node['action_required']) {
                    $node['action_required'] = json_decode($node['action_required'], true);
                }
                if ($node['conditions']) {
                    $node['conditions'] = json_decode($node['conditions'], true);
                }
            }

            // 获取统计信息
            $workflow['nodes'] = $nodes;
            $workflow['node_count'] = count($nodes);
            $workflow['project_count'] = $this->projectWorkflowModel->where('workflow_definition_id', $id)->countAllResults();

            return $workflow;

        } catch (\Exception $e) {
            log_message('error', '获取工作流详情失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 创建工作流
     * 
     * @param array $data 工作流数据
     * @return array
     */
    public function createWorkflow(array $data): array
    {
        try {
            // 检查编码是否已存在
            $existing = $this->workflowModel->where('code', $data['code'])
                ->where('version', $data['version'] ?? '1.0')
                ->first();

            if ($existing) {
                return [
                    'success' => false,
                    'message' => '工作流编码和版本号组合已存在'
                ];
            }

            // 设置默认值
            $data['version'] = $data['version'] ?? '1.0';
            $data['is_active'] = $data['is_active'] ?? 0;

            $workflowId = $this->workflowModel->insert($data);

            if ($workflowId) {
                return [
                    'success' => true,
                    'message' => '工作流创建成功',
                    'data' => ['id' => $workflowId]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '工作流创建失败'
                ];
            }

        } catch (DatabaseException $e) {
            log_message('error', '创建工作流失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '工作流创建失败：数据库错误'
            ];
        } catch (\Exception $e) {
            log_message('error', '创建工作流失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '工作流创建失败'
            ];
        }
    }

    /**
     * 更新工作流
     * 
     * @param int $id 工作流ID
     * @param array $data 工作流数据
     * @return array
     */
    public function updateWorkflow(int $id, array $data): array
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return [
                    'success' => false,
                    'message' => '工作流不存在'
                ];
            }

            // 检查编码是否已存在（排除当前记录）
            $existing = $this->workflowModel->where('code', $data['code'])
                ->where('version', $data['version'] ?? $workflow['version'])
                ->where('id !=', $id)
                ->first();

            if ($existing) {
                return [
                    'success' => false,
                    'message' => '工作流编码和版本号组合已存在'
                ];
            }

            $success = $this->workflowModel->update($id, $data);

            if ($success) {
                return [
                    'success' => true,
                    'message' => '工作流更新成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '工作流更新失败'
                ];
            }

        } catch (DatabaseException $e) {
            log_message('error', '更新工作流失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '工作流更新失败：数据库错误'
            ];
        } catch (\Exception $e) {
            log_message('error', '更新工作流失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '工作流更新失败'
            ];
        }
    }

    /**
     * 删除工作流
     * 
     * @param int $id 工作流ID
     * @return array
     */
    public function deleteWorkflow(int $id): array
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return [
                    'success' => false,
                    'message' => '工作流不存在'
                ];
            }

            // 检查是否有关联的项目工作流实例
            $projectCount = $this->projectWorkflowModel->where('workflow_definition_id', $id)->countAllResults();
            if ($projectCount > 0) {
                return [
                    'success' => false,
                    'message' => '该工作流已被项目使用，无法删除'
                ];
            }

            // 删除关联的节点定义
            $this->nodeModel->where('workflow_id', $id)->delete();

            // 删除工作流
            $success = $this->workflowModel->delete($id);

            if ($success) {
                return [
                    'success' => true,
                    'message' => '工作流删除成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '工作流删除失败'
                ];
            }

        } catch (DatabaseException $e) {
            log_message('error', '删除工作流失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '工作流删除失败：数据库错误'
            ];
        } catch (\Exception $e) {
            log_message('error', '删除工作流失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '工作流删除失败'
            ];
        }
    }

    /**
     * 激活工作流
     * 
     * @param int $id 工作流ID
     * @return array
     */
    public function activateWorkflow(int $id): array
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return [
                    'success' => false,
                    'message' => '工作流不存在'
                ];
            }

            // 验证工作流完整性
            $validation = $this->validateWorkflow($id);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'message' => '工作流配置不完整，无法激活：' . implode(', ', $validation['errors'])
                ];
            }

            // 停用同产品线的其他工作流
            $this->workflowModel->where('product_line', $workflow['product_line'])
                ->where('id !=', $id)
                ->set(['is_active' => 0])
                ->update();

            // 激活当前工作流
            $success = $this->workflowModel->update($id, ['is_active' => 1]);

            if ($success) {
                return [
                    'success' => true,
                    'message' => '工作流激活成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '工作流激活失败'
                ];
            }

        } catch (\Exception $e) {
            log_message('error', '激活工作流失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '激活工作流失败'
            ];
        }
    }

    /**
     * 停用工作流
     * 
     * @param int $id 工作流ID
     * @return array
     */
    public function deactivateWorkflow(int $id): array
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return [
                    'success' => false,
                    'message' => '工作流不存在'
                ];
            }

            $success = $this->workflowModel->update($id, ['is_active' => 0]);

            if ($success) {
                return [
                    'success' => true,
                    'message' => '工作流停用成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '工作流停用失败'
                ];
            }

        } catch (\Exception $e) {
            log_message('error', '停用工作流失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '停用工作流失败'
            ];
        }
    }

    /**
     * 复制工作流
     * 
     * @param int $id 工作流ID
     * @return array
     */
    public function copyWorkflow(int $id): array
    {
        try {
            $workflow = $this->workflowModel->find($id);
            if (!$workflow) {
                return [
                    'success' => false,
                    'message' => '工作流不存在'
                ];
            }

            // 生成新的编码
            $newCode = $workflow['code'] . '_copy_' . date('YmdHis');
            
            // 复制工作流
            $newWorkflowData = $workflow;
            unset($newWorkflowData['id'], $newWorkflowData['created_at'], $newWorkflowData['updated_at']);
            $newWorkflowData['name'] = $workflow['name'] . ' (副本)';
            $newWorkflowData['code'] = $newCode;
            $newWorkflowData['is_active'] = 0;

            $newWorkflowId = $this->workflowModel->insert($newWorkflowData);

            if ($newWorkflowId) {
                // 复制节点定义
                $nodes = $this->nodeModel->where('workflow_id', $id)->findAll();
                foreach ($nodes as $node) {
                    $newNodeData = $node;
                    unset($newNodeData['id'], $newNodeData['created_at'], $newNodeData['updated_at']);
                    $newNodeData['workflow_id'] = $newWorkflowId;
                    $this->nodeModel->insert($newNodeData);
                }

                return [
                    'success' => true,
                    'message' => '工作流复制成功',
                    'data' => ['id' => $newWorkflowId]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '工作流复制失败'
                ];
            }

        } catch (\Exception $e) {
            log_message('error', '复制工作流失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '复制工作流失败'
            ];
        }
    }

    /**
     * 验证工作流完整性
     * 
     * @param int $id 工作流ID
     * @return array
     */
    public function validateWorkflow(int $id): array
    {
        try {
            $errors = [];

            // 检查是否有节点
            $nodeCount = $this->nodeModel->where('workflow_id', $id)->countAllResults();
            if ($nodeCount === 0) {
                $errors[] = '工作流必须包含至少一个节点';
            }

            // 检查节点序号是否连续
            $nodes = $this->nodeModel->where('workflow_id', $id)
                ->orderBy('sequence', 'ASC')
                ->findAll();

            if (!empty($nodes)) {
                $sequences = array_column($nodes, 'sequence');
                $expectedSequences = range(1, count($sequences));
                if ($sequences !== $expectedSequences) {
                    $errors[] = '节点序号必须从1开始连续排列';
                }
            }

            return [
                'valid' => empty($errors),
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            log_message('error', '验证工作流失败: ' . $e->getMessage());
            return [
                'valid' => false,
                'errors' => ['验证工作流时发生错误']
            ];
        }
    }
}
