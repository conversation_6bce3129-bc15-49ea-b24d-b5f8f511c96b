<?php

namespace App\Services;

use App\Models\WorkflowDefinitionModel;
use App\Models\NodeDefinitionModel;
use App\Models\ProjectWorkflowModel;
use App\Models\ProjectNodeModel;
use App\Models\ProjectModel;
use App\Models\DepartmentModel;
use App\Models\UserDepartmentModel;
use App\Models\ProjectLogModel;

/**
 * 工作流引擎服务
 * 负责工作流的创建、执行、流转等核心业务逻辑
 */
class WorkflowEngine
{
    protected WorkflowDefinitionModel $workflowModel;
    protected NodeDefinitionModel $nodeModel;
    protected ProjectWorkflowModel $projectWorkflowModel;
    protected ProjectNodeModel $projectNodeModel;
    protected ProjectModel $projectModel;
    protected DepartmentModel $departmentModel;
    protected UserDepartmentModel $userDepartmentModel;
    protected ProjectLogModel $logModel;

    public function __construct()
    {
        $this->workflowModel = new WorkflowDefinitionModel();
        $this->nodeModel = new NodeDefinitionModel();
        $this->projectWorkflowModel = new ProjectWorkflowModel();
        $this->projectNodeModel = new ProjectNodeModel();
        $this->projectModel = new ProjectModel();
        $this->departmentModel = new DepartmentModel();
        $this->userDepartmentModel = new UserDepartmentModel();
        $this->logModel = new ProjectLogModel();
    }

    /**
     * 启动项目工作流
     * 
     * @param int $projectId 项目ID
     * @return array 操作结果
     */
    public function startProjectWorkflow(int $projectId): array
    {
        try {
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                return $this->errorResponse('项目不存在');
            }

            if ($project['status'] !== 'draft') {
                return $this->errorResponse('只有草稿状态的项目才能启动工作流');
            }

            // 检查是否已存在工作流实例
            $existingWorkflow = $this->projectWorkflowModel->getByProjectId($projectId);
            if ($existingWorkflow) {
                return $this->errorResponse('项目已存在工作流实例');
            }

            // 创建工作流实例
            $workflowId = $this->projectWorkflowModel->createProjectWorkflow($projectId, $project['product_line']);
            
            if (!$workflowId) {
                return $this->errorResponse('创建工作流实例失败');
            }

            // 更新项目状态
            $this->projectModel->update($projectId, ['status' => 'active']);

            // 记录日志
            $this->logModel->logAction(
                $projectId,
                null,
                'workflow_started',
                '工作流已启动',
                session('user_id'),
                ['workflow_id' => $workflowId]
            );

            return $this->successResponse('工作流启动成功', ['workflow_id' => $workflowId]);

        } catch (\Exception $e) {
            log_message('error', '启动工作流失败: ' . $e->getMessage());
            return $this->errorResponse('启动工作流失败: ' . $e->getMessage());
        }
    }

    /**
     * 完成节点处理
     * 
     * @param int $nodeId 节点ID
     * @param array $resultData 处理结果数据
     * @param int $userId 处理人ID
     * @return array 操作结果
     */
    public function completeNode(int $nodeId, array $resultData, int $userId): array
    {
        try {
            $node = $this->projectNodeModel->find($nodeId);
            if (!$node) {
                return $this->errorResponse('节点不存在');
            }

            if (!in_array($node['status'], ['pending', 'running'])) {
                return $this->errorResponse('节点状态不允许完成操作');
            }

            // 验证处理人权限
            if ($node['assignee_id'] && $node['assignee_id'] !== $userId) {
                return $this->errorResponse('您没有权限处理此节点');
            }

            // 完成节点
            $success = $this->projectNodeModel->completeNode($nodeId, $resultData, $userId);
            if (!$success) {
                return $this->errorResponse('完成节点失败');
            }

            // 获取工作流信息
            $workflow = $this->projectWorkflowModel->find($node['project_workflow_id']);
            $nodeDefinition = $this->nodeModel->find($node['node_definition_id']);

            // 流转到下一个节点
            $this->moveToNextNode($workflow, $nodeDefinition, $resultData);

            return $this->successResponse('节点处理完成');

        } catch (\Exception $e) {
            log_message('error', '完成节点失败: ' . $e->getMessage());
            return $this->errorResponse('完成节点失败: ' . $e->getMessage());
        }
    }

    /**
     * 驳回节点
     * 
     * @param int $nodeId 节点ID
     * @param string $rejectReason 驳回原因
     * @param int $userId 处理人ID
     * @return array 操作结果
     */
    public function rejectNode(int $nodeId, string $rejectReason, int $userId): array
    {
        try {
            $node = $this->projectNodeModel->find($nodeId);
            if (!$node) {
                return $this->errorResponse('节点不存在');
            }

            if (!in_array($node['status'], ['pending', 'running'])) {
                return $this->errorResponse('节点状态不允许驳回操作');
            }

            $nodeDefinition = $this->nodeModel->find($node['node_definition_id']);
            if (!$nodeDefinition['can_reject']) {
                return $this->errorResponse('此节点不支持驳回操作');
            }

            // 验证处理人权限
            if ($node['assignee_id'] && $node['assignee_id'] !== $userId) {
                return $this->errorResponse('您没有权限处理此节点');
            }

            // 驳回节点
            $success = $this->projectNodeModel->rejectNode($nodeId, $rejectReason, $userId);
            if (!$success) {
                return $this->errorResponse('驳回节点失败');
            }

            // 获取工作流信息并驳回到目标节点
            $workflow = $this->projectWorkflowModel->find($node['project_workflow_id']);
            if ($nodeDefinition['reject_to_node']) {
                $this->projectWorkflowModel->rejectToNode(
                    $workflow['id'],
                    $nodeDefinition['reject_to_node'],
                    $rejectReason
                );
            }

            return $this->successResponse('节点已驳回');

        } catch (\Exception $e) {
            log_message('error', '驳回节点失败: ' . $e->getMessage());
            return $this->errorResponse('驳回节点失败: ' . $e->getMessage());
        }
    }

    /**
     * 分配节点处理人
     * 
     * @param int $nodeId 节点ID
     * @param int $assigneeId 处理人ID
     * @param int $operatorId 操作人ID
     * @return array 操作结果
     */
    public function assignNode(int $nodeId, int $assigneeId, int $operatorId): array
    {
        try {
            $node = $this->projectNodeModel->find($nodeId);
            if (!$node) {
                return $this->errorResponse('节点不存在');
            }

            if ($node['status'] !== 'pending') {
                return $this->errorResponse('只有待处理状态的节点才能分配处理人');
            }

            // 验证分配权限（通常是部门负责人才能分配）
            if (!$this->canAssignNode($operatorId, $node)) {
                return $this->errorResponse('您没有权限分配此节点');
            }

            // 验证被分配人是否有处理权限
            if (!$this->canUserHandleNode($assigneeId, $node)) {
                return $this->errorResponse('被分配人没有处理此节点的权限');
            }

            // 分配节点
            $success = $this->projectNodeModel->assignNode($nodeId, $assigneeId);
            if (!$success) {
                return $this->errorResponse('分配节点失败');
            }

            // 记录日志
            $workflow = $this->projectWorkflowModel->find($node['project_workflow_id']);
            $this->logModel->logAction(
                $workflow['project_id'],
                $nodeId,
                'node_assigned',
                "节点已分配给用户 {$assigneeId}",
                $operatorId,
                ['assignee_id' => $assigneeId]
            );

            return $this->successResponse('节点分配成功');

        } catch (\Exception $e) {
            log_message('error', '分配节点失败: ' . $e->getMessage());
            return $this->errorResponse('分配节点失败: ' . $e->getMessage());
        }
    }

    /**
     * 添加协同部门
     * 
     * @param int $projectId 项目ID
     * @param array $departmentIds 部门ID数组
     * @param int $operatorId 操作人ID
     * @return array 操作结果
     */
    public function addCollaborativeDepartments(int $projectId, array $departmentIds, int $operatorId): array
    {
        try {
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                return $this->errorResponse('项目不存在');
            }

            // 验证部门是否支持该产品线
            foreach ($departmentIds as $departmentId) {
                if (!$this->departmentModel->supportsProductLine($departmentId, $project['product_line'])) {
                    return $this->errorResponse("部门 {$departmentId} 不支持产品线 {$project['product_line']}");
                }
            }

            // 添加协同部门关联
            $collaborationModel = new \App\Models\ProjectCollaborationModel();
            foreach ($departmentIds as $departmentId) {
                $collaborationModel->insert([
                    'project_id' => $projectId,
                    'department_id' => $departmentId,
                    'status' => 'pending',
                    'assigned_at' => date('Y-m-d H:i:s')
                ]);
            }

            // 创建协同节点实例
            $this->createCollaborativeNodes($projectId, $departmentIds);

            // 记录日志
            $this->logModel->logAction(
                $projectId,
                null,
                'collaboration_added',
                '添加协同部门: ' . implode(', ', $departmentIds),
                $operatorId,
                ['department_ids' => $departmentIds]
            );

            return $this->successResponse('协同部门添加成功');

        } catch (\Exception $e) {
            log_message('error', '添加协同部门失败: ' . $e->getMessage());
            return $this->errorResponse('添加协同部门失败: ' . $e->getMessage());
        }
    }

    /**
     * 流转到下一个节点
     * 
     * @param array $workflow 工作流实例
     * @param array $currentNodeDefinition 当前节点定义
     * @param array $resultData 处理结果数据
     * @return bool
     */
    protected function moveToNextNode(array $workflow, array $currentNodeDefinition, array $resultData): bool
    {
        // 获取下一个节点定义
        $nextNode = $this->nodeModel->getNextNode(
            $workflow['workflow_definition_id'],
            $currentNodeDefinition['sequence'],
            $resultData
        );

        if (!$nextNode) {
            // 没有下一个节点，完成工作流
            $this->projectWorkflowModel->completeWorkflow($workflow['id']);
            $this->projectModel->completeProject($workflow['project_id']);
            
            // 发送完成邮件
            $this->sendCompletionEmail($workflow['project_id']);
            
            return true;
        }

        // 检查是否是并行节点（协同节点）
        if ($nextNode['is_parallel']) {
            $this->handleParallelNodes($workflow, $nextNode);
        } else {
            // 创建下一个节点实例
            $this->projectNodeModel->createNextNode($workflow['id'], $nextNode);
        }

        // 更新工作流当前节点
        $this->projectWorkflowModel->moveToNextNode($workflow['id'], $nextNode['node_code']);

        return true;
    }

    /**
     * 处理并行节点（协同节点）
     * 
     * @param array $workflow 工作流实例
     * @param array $nodeDefinition 节点定义
     * @return void
     */
    protected function handleParallelNodes(array $workflow, array $nodeDefinition): void
    {
        // 获取项目的协同部门
        $collaborationModel = new \App\Models\ProjectCollaborationModel();
        $collaborations = $collaborationModel->where('project_id', $workflow['project_id'])
                                            ->where('status', 'active')
                                            ->findAll();

        // 为每个协同部门创建节点实例
        foreach ($collaborations as $collaboration) {
            $this->projectNodeModel->createNextNode(
                $workflow['id'],
                $nodeDefinition,
                $collaboration['department_id']
            );
        }
    }

    /**
     * 创建协同节点实例
     * 
     * @param int $projectId 项目ID
     * @param array $departmentIds 部门ID数组
     * @return void
     */
    protected function createCollaborativeNodes(int $projectId, array $departmentIds): void
    {
        $workflow = $this->projectWorkflowModel->getByProjectId($projectId);
        if (!$workflow) {
            return;
        }

        // 获取当前序号的协同节点定义
        $currentNode = $this->nodeModel->getByNodeCode(
            $workflow['workflow_definition_id'],
            $workflow['current_node_code']
        );

        if (!$currentNode) {
            return;
        }

        // 获取协同节点定义
        $collaborativeNodes = $this->nodeModel->getParallelNodes(
            $workflow['workflow_definition_id'],
            $currentNode['sequence'] + 1
        );

        // 为每个部门创建协同节点实例
        foreach ($departmentIds as $departmentId) {
            foreach ($collaborativeNodes as $nodeDefinition) {
                $this->projectNodeModel->createNextNode(
                    $workflow['id'],
                    $nodeDefinition,
                    $departmentId
                );
            }
        }
    }

    /**
     * 检查用户是否可以分配节点
     * 
     * @param int $userId 用户ID
     * @param array $node 节点信息
     * @return bool
     */
    protected function canAssignNode(int $userId, array $node): bool
    {
        // 获取节点定义
        $nodeDefinition = $this->nodeModel->find($node['node_definition_id']);
        
        // 主节点通常由主部门负责人分配
        if ($nodeDefinition['node_type'] === 'main') {
            $workflow = $this->projectWorkflowModel->find($node['project_workflow_id']);
            $project = $this->projectModel->find($workflow['project_id']);
            
            return $this->userDepartmentModel->hasUserRole(
                $userId,
                $project['main_department_id'],
                'manager'
            );
        }
        
        // 协同节点由协同部门负责人分配
        if ($nodeDefinition['node_type'] === 'collaborative' && $node['department_id']) {
            return $this->userDepartmentModel->hasUserRole(
                $userId,
                $node['department_id'],
                'manager'
            );
        }
        
        return false;
    }

    /**
     * 检查用户是否可以处理节点
     * 
     * @param int $userId 用户ID
     * @param array $node 节点信息
     * @return bool
     */
    protected function canUserHandleNode(int $userId, array $node): bool
    {
        $nodeDefinition = $this->nodeModel->find($node['node_definition_id']);
        $workflow = $this->projectWorkflowModel->find($node['project_workflow_id']);
        $project = $this->projectModel->find($workflow['project_id']);

        switch ($nodeDefinition['assignee_type']) {
            case 'sales':
                return $project['sales_user_id'] === $userId;
                
            case 'business':
                return $project['business_user_id'] === $userId;
                
            case 'main_manager':
                return $this->userDepartmentModel->hasUserRole(
                    $userId,
                    $project['main_department_id'],
                    'manager'
                );
                
            case 'main_executor':
                return $this->userDepartmentModel->hasUserRole(
                    $userId,
                    $project['main_department_id'],
                    'executor'
                );
                
            case 'collab_manager':
                return $node['department_id'] && $this->userDepartmentModel->hasUserRole(
                    $userId,
                    $node['department_id'],
                    'manager'
                );
                
            case 'collab_executor':
                return $node['department_id'] && $this->userDepartmentModel->hasUserRole(
                    $userId,
                    $node['department_id'],
                    'executor'
                );
                
            case 'collab_analyst':
                return $node['department_id'] && $this->userDepartmentModel->hasUserRole(
                    $userId,
                    $node['department_id'],
                    'analyst'
                );
                
            default:
                return false;
        }
    }

    /**
     * 获取工作流状态
     *
     * @param int $projectId 项目ID
     * @return array
     */
    public function getWorkflowStatus(int $projectId): array
    {
        try {
            $workflow = $this->projectWorkflowModel->getByProjectId($projectId);
            if (!$workflow) {
                return $this->errorResponse('项目工作流不存在');
            }

            // 获取进度信息
            $progress = $this->projectWorkflowModel->getProgress($workflow['id']);

            // 获取当前节点信息
            $currentNode = null;
            if ($workflow['current_node_code']) {
                $currentNode = $this->projectNodeModel
                    ->select('project_nodes.*, node_definitions.node_name, node_definitions.action_required, users.real_name as assignee_name')
                    ->join('node_definitions', 'node_definitions.id = project_nodes.node_definition_id')
                    ->join('users', 'users.id = project_nodes.assignee_id', 'left')
                    ->where('project_nodes.project_workflow_id', $workflow['id'])
                    ->where('project_nodes.node_code', $workflow['current_node_code'])
                    ->where('project_nodes.status', 'running')
                    ->first();
            }

            // 获取执行历史
            $history = $this->projectWorkflowModel->getExecutionHistory($workflow['id']);

            return $this->successResponse('获取工作流状态成功', [
                'workflow' => $workflow,
                'progress' => $progress,
                'current_node' => $currentNode,
                'history' => $history
            ]);

        } catch (\Exception $e) {
            log_message('error', '获取工作流状态失败: ' . $e->getMessage());
            return $this->errorResponse('获取工作流状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取用户待处理任务
     *
     * @param int $userId 用户ID
     * @param array $filters 筛选条件
     * @return array
     */
    public function getUserPendingTasks(int $userId, array $filters = []): array
    {
        try {
            $tasks = $this->projectNodeModel->getPendingTasks($userId, $filters);

            // 增强任务信息
            foreach ($tasks as &$task) {
                $task['action_required'] = is_string($task['action_required'])
                    ? json_decode($task['action_required'], true)
                    : $task['action_required'];

                // 计算任务优先级
                $task['priority'] = $this->calculateTaskPriority($task);

                // 计算超时状态
                $task['is_timeout'] = $this->isTaskTimeout($task);
            }

            return $this->successResponse('获取待处理任务成功', $tasks);

        } catch (\Exception $e) {
            log_message('error', '获取待处理任务失败: ' . $e->getMessage());
            return $this->errorResponse('获取待处理任务失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量处理节点
     *
     * @param array $nodeActions 节点操作数组
     * @param int $userId 操作用户ID
     * @return array
     */
    public function batchProcessNodes(array $nodeActions, int $userId): array
    {
        try {
            $results = [];
            $successCount = 0;
            $failCount = 0;

            foreach ($nodeActions as $action) {
                $nodeId = $action['node_id'];
                $actionType = $action['action']; // 'complete' or 'reject'

                if ($actionType === 'complete') {
                    $result = $this->completeNode($nodeId, $action['result_data'] ?? [], $userId);
                } elseif ($actionType === 'reject') {
                    $result = $this->rejectNode($nodeId, $action['reject_reason'] ?? '', $userId);
                } else {
                    $result = $this->errorResponse('不支持的操作类型');
                }

                $results[] = [
                    'node_id' => $nodeId,
                    'action' => $actionType,
                    'result' => $result
                ];

                if ($result['success']) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            return $this->successResponse('批量处理完成', [
                'total' => count($nodeActions),
                'success' => $successCount,
                'failed' => $failCount,
                'details' => $results
            ]);

        } catch (\Exception $e) {
            log_message('error', '批量处理节点失败: ' . $e->getMessage());
            return $this->errorResponse('批量处理节点失败: ' . $e->getMessage());
        }
    }

    /**
     * 计算任务优先级
     *
     * @param array $task 任务信息
     * @return string
     */
    protected function calculateTaskPriority(array $task): string
    {
        $createdTime = strtotime($task['created_at']);
        $hoursElapsed = (time() - $createdTime) / 3600;

        if ($hoursElapsed > 72) {
            return 'high';
        } elseif ($hoursElapsed > 24) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * 检查任务是否超时
     *
     * @param array $task 任务信息
     * @return bool
     */
    protected function isTaskTimeout(array $task): bool
    {
        if (!$task['due_date']) {
            return false;
        }

        return strtotime($task['due_date']) < time();
    }

    /**
     * 发送完成邮件
     *
     * @param int $projectId 项目ID
     * @return void
     */
    protected function sendCompletionEmail(int $projectId): void
    {
        // TODO: 实现邮件发送逻辑
        // 这里可以集成邮件服务，发送项目完成通知给客户
        $project = $this->projectModel->find($projectId);
        if ($project && $project['customer_email']) {
            // 发送邮件通知客户项目已完成
            log_message('info', "项目 {$project['project_no']} 已完成，需要发送邮件给 {$project['customer_email']}");
        }
    }

    /**
     * 成功响应
     * 
     * @param string $message 消息
     * @param array $data 数据
     * @return array
     */
    protected function successResponse(string $message, array $data = []): array
    {
        return [
            'success' => true,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 错误响应
     * 
     * @param string $message 错误消息
     * @return array
     */
    protected function errorResponse(string $message): array
    {
        return [
            'success' => false,
            'message' => $message,
            'data' => []
        ];
    }
}
