<?php

namespace App\Services;

use App\Models\NodeDefinitionModel;
use App\Models\WorkflowDefinitionModel;
use App\Models\ProjectNodeModel;
use CodeIgniter\Database\Exceptions\DatabaseException;

/**
 * 节点定义服务
 * 封装节点定义相关的业务逻辑
 */
class NodeDefinitionService
{
    protected NodeDefinitionModel $nodeModel;
    protected WorkflowDefinitionModel $workflowModel;
    protected ProjectNodeModel $projectNodeModel;

    /**
     * 构造函数
     * 初始化模型
     */
    public function __construct()
    {
        $this->nodeModel = new NodeDefinitionModel();
        $this->workflowModel = new WorkflowDefinitionModel();
        $this->projectNodeModel = new ProjectNodeModel();
    }

    /**
     * 获取节点列表
     * 
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array
     */
    public function getNodeList(array $filters = [], int $page = 1, int $perPage = 20): array
    {
        try {
            $builder = $this->nodeModel->select('node_definitions.*, workflow_definitions.name as workflow_name')
                ->join('workflow_definitions', 'workflow_definitions.id = node_definitions.workflow_id', 'left');

            // 应用筛选条件
            if (!empty($filters['workflow_id'])) {
                $builder = $builder->where('node_definitions.workflow_id', $filters['workflow_id']);
            }

            if (!empty($filters['node_type'])) {
                $builder = $builder->where('node_definitions.node_type', $filters['node_type']);
            }

            if (!empty($filters['assignee_type'])) {
                $builder = $builder->where('node_definitions.assignee_type', $filters['assignee_type']);
            }

            if (!empty($filters['keyword'])) {
                $builder = $builder->groupStart()
                    ->like('node_definitions.node_name', $filters['keyword'])
                    ->orLike('node_definitions.node_code', $filters['keyword'])
                    ->groupEnd();
            }

            // 获取总数
            $total = $builder->countAllResults(false);

            // 分页查询
            $nodes = $builder->orderBy('node_definitions.workflow_id', 'ASC')
                ->orderBy('node_definitions.sequence', 'ASC')
                ->paginate($perPage, 'default', $page);

            // 获取分页器
            $pager = $this->nodeModel->pager;

            // 解析JSON字段
            foreach ($nodes as &$node) {
                if ($node['action_required']) {
                    $node['action_required'] = json_decode($node['action_required'], true);
                }
                if ($node['conditions']) {
                    $node['conditions'] = json_decode($node['conditions'], true);
                }
            }

            return [
                'success' => true,
                'data' => $nodes,
                'total' => $total,
                'pager' => $pager
            ];

        } catch (\Exception $e) {
            log_message('error', '获取节点列表失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '获取节点列表失败',
                'data' => [],
                'total' => 0,
                'pager' => null
            ];
        }
    }

    /**
     * 获取节点详情
     * 
     * @param int $id 节点ID
     * @return array|null
     */
    public function getNodeDetail(int $id): ?array
    {
        try {
            $node = $this->nodeModel->select('node_definitions.*, workflow_definitions.name as workflow_name')
                ->join('workflow_definitions', 'workflow_definitions.id = node_definitions.workflow_id', 'left')
                ->find($id);

            if (!$node) {
                return null;
            }

            // 解析JSON字段
            if ($node['action_required']) {
                $node['action_required'] = json_decode($node['action_required'], true);
            }
            if ($node['conditions']) {
                $node['conditions'] = json_decode($node['conditions'], true);
            }

            return $node;

        } catch (\Exception $e) {
            log_message('error', '获取节点详情失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 创建节点
     * 
     * @param array $data 节点数据
     * @return array
     */
    public function createNode(array $data): array
    {
        try {
            // 检查工作流是否存在
            $workflow = $this->workflowModel->find($data['workflow_id']);
            if (!$workflow) {
                return [
                    'success' => false,
                    'message' => '工作流不存在'
                ];
            }

            // 检查节点编码是否在同一工作流中已存在
            $existing = $this->nodeModel->where('workflow_id', $data['workflow_id'])
                ->where('node_code', $data['node_code'])
                ->first();

            if ($existing) {
                return [
                    'success' => false,
                    'message' => '节点编码在该工作流中已存在'
                ];
            }

            // 如果没有指定序号，自动分配
            if (empty($data['sequence'])) {
                $maxSequence = $this->nodeModel->where('workflow_id', $data['workflow_id'])
                    ->selectMax('sequence')
                    ->first();
                $data['sequence'] = ($maxSequence['sequence'] ?? 0) + 1;
            } else {
                // 检查序号是否已存在
                $existingSequence = $this->nodeModel->where('workflow_id', $data['workflow_id'])
                    ->where('sequence', $data['sequence'])
                    ->first();

                if ($existingSequence) {
                    // 将现有序号及之后的节点序号都加1
                    $this->nodeModel->where('workflow_id', $data['workflow_id'])
                        ->where('sequence >=', $data['sequence'])
                        ->set('sequence', 'sequence + 1', false)
                        ->update();
                }
            }

            // 设置默认值
            $data['can_reject'] = $data['can_reject'] ?? 0;
            $data['auto_assign'] = $data['auto_assign'] ?? 0;
            $data['is_parallel'] = $data['is_parallel'] ?? 0;

            $nodeId = $this->nodeModel->insert($data);

            if ($nodeId) {
                return [
                    'success' => true,
                    'message' => '节点创建成功',
                    'data' => ['id' => $nodeId]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '节点创建失败'
                ];
            }

        } catch (DatabaseException $e) {
            log_message('error', '创建节点失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '节点创建失败：数据库错误'
            ];
        } catch (\Exception $e) {
            log_message('error', '创建节点失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '节点创建失败'
            ];
        }
    }

    /**
     * 更新节点
     * 
     * @param int $id 节点ID
     * @param array $data 节点数据
     * @return array
     */
    public function updateNode(int $id, array $data): array
    {
        try {
            $node = $this->nodeModel->find($id);
            if (!$node) {
                return [
                    'success' => false,
                    'message' => '节点不存在'
                ];
            }

            // 检查节点编码是否在同一工作流中已存在（排除当前记录）
            $existing = $this->nodeModel->where('workflow_id', $data['workflow_id'])
                ->where('node_code', $data['node_code'])
                ->where('id !=', $id)
                ->first();

            if ($existing) {
                return [
                    'success' => false,
                    'message' => '节点编码在该工作流中已存在'
                ];
            }

            // 如果序号发生变化，需要重新排序
            if (isset($data['sequence']) && $data['sequence'] != $node['sequence']) {
                $this->reorderNodes($data['workflow_id'], $node['sequence'], $data['sequence']);
            }

            $success = $this->nodeModel->update($id, $data);

            if ($success) {
                return [
                    'success' => true,
                    'message' => '节点更新成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '节点更新失败'
                ];
            }

        } catch (DatabaseException $e) {
            log_message('error', '更新节点失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '节点更新失败：数据库错误'
            ];
        } catch (\Exception $e) {
            log_message('error', '更新节点失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '节点更新失败'
            ];
        }
    }

    /**
     * 删除节点
     * 
     * @param int $id 节点ID
     * @return array
     */
    public function deleteNode(int $id): array
    {
        try {
            $node = $this->nodeModel->find($id);
            if (!$node) {
                return [
                    'success' => false,
                    'message' => '节点不存在'
                ];
            }

            // 检查是否有关联的项目节点实例
            $projectNodeCount = $this->projectNodeModel->where('node_definition_id', $id)->countAllResults();
            if ($projectNodeCount > 0) {
                return [
                    'success' => false,
                    'message' => '该节点已被项目使用，无法删除'
                ];
            }

            // 删除节点
            $success = $this->nodeModel->delete($id);

            if ($success) {
                // 重新排序剩余节点
                $this->reorderNodesAfterDelete($node['workflow_id'], $node['sequence']);

                return [
                    'success' => true,
                    'message' => '节点删除成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '节点删除失败'
                ];
            }

        } catch (DatabaseException $e) {
            log_message('error', '删除节点失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '节点删除失败：数据库错误'
            ];
        } catch (\Exception $e) {
            log_message('error', '删除节点失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '节点删除失败'
            ];
        }
    }

    /**
     * 批量排序节点
     * 
     * @param array $data 排序数据 [['id' => 1, 'sequence' => 1], ...]
     * @return array
     */
    public function sortNodes(array $data): array
    {
        try {
            $db = \Config\Database::connect();
            $db->transStart();

            foreach ($data as $item) {
                $this->nodeModel->update($item['id'], ['sequence' => $item['sequence']]);
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                return [
                    'success' => false,
                    'message' => '节点排序失败'
                ];
            }

            return [
                'success' => true,
                'message' => '节点排序成功'
            ];

        } catch (\Exception $e) {
            log_message('error', '节点排序失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '节点排序失败'
            ];
        }
    }

    /**
     * 重新排序节点（移动节点时使用）
     * 
     * @param int $workflowId 工作流ID
     * @param int $oldSequence 原序号
     * @param int $newSequence 新序号
     * @return void
     */
    private function reorderNodes(int $workflowId, int $oldSequence, int $newSequence): void
    {
        if ($oldSequence == $newSequence) {
            return;
        }

        if ($oldSequence < $newSequence) {
            // 向后移动：将中间的节点序号减1
            $this->nodeModel->where('workflow_id', $workflowId)
                ->where('sequence >', $oldSequence)
                ->where('sequence <=', $newSequence)
                ->set('sequence', 'sequence - 1', false)
                ->update();
        } else {
            // 向前移动：将中间的节点序号加1
            $this->nodeModel->where('workflow_id', $workflowId)
                ->where('sequence >=', $newSequence)
                ->where('sequence <', $oldSequence)
                ->set('sequence', 'sequence + 1', false)
                ->update();
        }
    }

    /**
     * 删除节点后重新排序
     * 
     * @param int $workflowId 工作流ID
     * @param int $deletedSequence 被删除节点的序号
     * @return void
     */
    private function reorderNodesAfterDelete(int $workflowId, int $deletedSequence): void
    {
        // 将删除节点之后的所有节点序号减1
        $this->nodeModel->where('workflow_id', $workflowId)
            ->where('sequence >', $deletedSequence)
            ->set('sequence', 'sequence - 1', false)
            ->update();
    }
}
