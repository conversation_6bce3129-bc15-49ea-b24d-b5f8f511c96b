<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateSupportTables extends Migration
{
    public function up()
    {
        // 1. 项目节点实例表
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'project_workflow_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'node_definition_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'node_code' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'assignee_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'department_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
                'comment' => '处理部门ID（协同节点使用）',
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'running', 'completed', 'rejected', 'skipped'],
                'default' => 'pending',
            ],
            'started_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'completed_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'due_date' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'result_data' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => '处理结果数据，JSON格式',
            ],
            'reject_reason' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addKey('project_workflow_id');
        $this->forge->addKey('node_definition_id');
        $this->forge->addKey('node_code');
        $this->forge->addKey('assignee_id');
        $this->forge->addKey('department_id');
        $this->forge->addKey('status');
        $this->forge->addKey('started_at');
        $this->forge->createTable('project_nodes', true, ['ENGINE' => 'InnoDB', 'CHARSET' => 'utf8mb4', 'COLLATE' => 'utf8mb4_unicode_ci']);

        // 2. 项目协同部门表
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'project_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'department_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'collaboration_content' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'active', 'completed', 'cancelled'],
                'default' => 'pending',
            ],
            'assigned_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'completed_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addKey(['project_id', 'department_id'], false, 'uk_project_department');
        $this->forge->addKey('project_id');
        $this->forge->addKey('department_id');
        $this->forge->addKey('status');
        $this->forge->createTable('project_collaborations', true, ['ENGINE' => 'InnoDB', 'CHARSET' => 'utf8mb4', 'COLLATE' => 'utf8mb4_unicode_ci']);
    }

    public function down()
    {
        $this->forge->dropTable('project_collaborations', true);
        $this->forge->dropTable('project_nodes', true);
    }
}
