<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateNodeInstanceTables extends Migration
{
    public function up()
    {
        // 1. 节点定义表
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'workflow_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'node_code' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'node_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'node_type' => [
                'type' => 'ENUM',
                'constraint' => ['main', 'collaborative'],
                'default' => 'main',
                'comment' => '节点类型：main=主节点，collaborative=协同节点',
            ],
            'sequence' => [
                'type' => 'INT',
                'constraint' => 11,
                'comment' => '节点序号',
            ],
            'assignee_type' => [
                'type' => 'ENUM',
                'constraint' => ['sales', 'main_manager', 'main_executor', 'collab_manager', 'collab_executor', 'collab_analyst', 'business'],
                'comment' => '处理人类型',
            ],
            'action_required' => [
                'type' => 'JSON',
                'comment' => '需要执行的操作，JSON格式',
            ],
            'can_reject' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'comment' => '是否可以驳回：1=是，0=否',
            ],
            'reject_to_node' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'comment' => '驳回到的节点编码',
            ],
            'auto_assign' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'comment' => '是否自动分配：1=是，0=否',
            ],
            'is_parallel' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'comment' => '是否并行节点：1=是，0=否',
            ],
            'conditions' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => '节点条件，JSON格式',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addKey(['workflow_id', 'node_code'], false, 'uk_workflow_node_code');
        $this->forge->addKey('workflow_id');
        $this->forge->addKey('node_type');
        $this->forge->addKey('sequence');
        $this->forge->createTable('node_definitions', true, ['ENGINE' => 'InnoDB', 'CHARSET' => 'utf8mb4', 'COLLATE' => 'utf8mb4_unicode_ci']);

        // 2. 项目工作流实例表
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'project_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'workflow_definition_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'current_node_code' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'running', 'completed', 'cancelled', 'rejected'],
                'default' => 'pending',
            ],
            'started_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'completed_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addKey('project_id', false, 'uk_project_workflow');
        $this->forge->addKey('workflow_definition_id');
        $this->forge->addKey('current_node_code');
        $this->forge->addKey('status');
        $this->forge->createTable('project_workflows', true, ['ENGINE' => 'InnoDB', 'CHARSET' => 'utf8mb4', 'COLLATE' => 'utf8mb4_unicode_ci']);
    }

    public function down()
    {
        $this->forge->dropTable('project_workflows', true);
        $this->forge->dropTable('node_definitions', true);
    }
}
