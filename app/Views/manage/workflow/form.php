<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $workflow ? '编辑工作流' : '新增工作流' ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-sitemap me-2"></i>
                        <?= $workflow ? '编辑工作流' : '新增工作流' ?>
                    </h2>
                    <a href="/manage/workflows" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">基本信息</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="<?= $workflow ? '/manage/workflows/' . $workflow['id'] : '/manage/workflows' ?>" id="workflowForm">
                                    <?php if ($workflow): ?>
                                        <input type="hidden" name="_method" value="PUT">
                                    <?php endif; ?>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="name" class="form-label">工作流名称 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="name" name="name" 
                                                       value="<?= old('name', $workflow['name'] ?? '') ?>" required>
                                                <div class="form-text">请输入工作流的显示名称</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="code" class="form-label">工作流编码 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="code" name="code" 
                                                       value="<?= old('code', $workflow['code'] ?? '') ?>" required>
                                                <div class="form-text">唯一标识符，建议使用英文字母和下划线</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="product_line" class="form-label">产品线 <span class="text-danger">*</span></label>
                                                <select class="form-select" id="product_line" name="product_line" required>
                                                    <option value="">请选择产品线</option>
                                                    <?php foreach ($productLines as $line): ?>
                                                        <option value="<?= $line ?>" 
                                                                <?= old('product_line', $workflow['product_line'] ?? '') === $line ? 'selected' : '' ?>>
                                                            产品线<?= $line ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <div class="form-text">选择该工作流适用的产品线</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="version" class="form-label">版本号 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="version" name="version" 
                                                       value="<?= old('version', $workflow['version'] ?? '1.0') ?>" required>
                                                <div class="form-text">版本号格式：如 1.0、1.1、2.0</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="description" class="form-label">描述</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"><?= old('description', $workflow['description'] ?? '') ?></textarea>
                                        <div class="form-text">工作流的详细描述（可选）</div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                                                   <?= old('is_active', $workflow['is_active'] ?? 0) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_active">
                                                激活工作流
                                            </label>
                                            <div class="form-text">激活后该工作流将可用于创建项目</div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <a href="/manage/workflows" class="btn btn-secondary me-2">取消</a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>
                                            <?= $workflow ? '更新' : '创建' ?>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">帮助信息</h5>
                            </div>
                            <div class="card-body">
                                <h6><i class="fas fa-info-circle text-info me-1"></i>填写说明</h6>
                                <ul class="list-unstyled small">
                                    <li><strong>工作流名称：</strong>用于显示的友好名称</li>
                                    <li><strong>工作流编码：</strong>系统内部使用的唯一标识</li>
                                    <li><strong>产品线：</strong>该工作流适用的产品线类型</li>
                                    <li><strong>版本号：</strong>用于版本管理和升级</li>
                                </ul>

                                <hr>

                                <h6><i class="fas fa-lightbulb text-warning me-1"></i>注意事项</h6>
                                <ul class="list-unstyled small">
                                    <li>• 工作流编码在系统中必须唯一</li>
                                    <li>• 激活的工作流会停用同产品线的其他工作流</li>
                                    <li>• 创建后需要添加节点才能正常使用</li>
                                    <li>• 已被项目使用的工作流不能删除</li>
                                </ul>

                                <?php if ($workflow): ?>
                                    <hr>
                                    <h6><i class="fas fa-chart-bar text-success me-1"></i>统计信息</h6>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="border rounded p-2">
                                                <div class="h5 mb-0"><?= $workflow['node_count'] ?? 0 ?></div>
                                                <small class="text-muted">节点数</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="border rounded p-2">
                                                <div class="h5 mb-0"><?= $workflow['project_count'] ?? 0 ?></div>
                                                <small class="text-muted">项目数</small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.all.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 表单验证
            $('#workflowForm').on('submit', function(e) {
                const name = $('#name').val().trim();
                const code = $('#code').val().trim();
                const productLine = $('#product_line').val();
                const version = $('#version').val().trim();

                if (!name || !code || !productLine || !version) {
                    e.preventDefault();
                    Swal.fire('错误', '请填写所有必填字段', 'error');
                    return false;
                }

                // 验证编码格式
                const codePattern = /^[a-zA-Z][a-zA-Z0-9_]*$/;
                if (!codePattern.test(code)) {
                    e.preventDefault();
                    Swal.fire('错误', '工作流编码必须以字母开头，只能包含字母、数字和下划线', 'error');
                    return false;
                }

                // 验证版本号格式
                const versionPattern = /^\d+(\.\d+)*$/;
                if (!versionPattern.test(version)) {
                    e.preventDefault();
                    Swal.fire('错误', '版本号格式不正确，请使用如 1.0、1.1、2.0 的格式', 'error');
                    return false;
                }
            });

            // 自动生成编码
            $('#name').on('blur', function() {
                const name = $(this).val().trim();
                const code = $('#code').val().trim();
                
                if (name && !code) {
                    // 简单的拼音转换（这里只是示例，实际项目中可能需要更完善的转换）
                    const generatedCode = name.toLowerCase()
                        .replace(/\s+/g, '_')
                        .replace(/[^a-z0-9_]/g, '');
                    $('#code').val(generatedCode);
                }
            });
        });

        // 显示错误消息
        <?php if (session('errors')): ?>
            const errors = <?= json_encode(session('errors')) ?>;
            let errorMessage = '请修正以下错误：\n';
            for (const field in errors) {
                errorMessage += '• ' + errors[field] + '\n';
            }
            Swal.fire('验证错误', errorMessage, 'error');
        <?php endif; ?>

        <?php if (session('error')): ?>
            Swal.fire('错误', '<?= session('error') ?>', 'error');
        <?php endif; ?>
    </script>
</body>
</html>
