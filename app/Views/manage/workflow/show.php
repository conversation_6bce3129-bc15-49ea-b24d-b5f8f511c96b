<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作流详情 - <?= esc($workflow['name']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .workflow-node {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .workflow-node.main-node {
            border-color: #0d6efd;
            background: #e7f1ff;
        }
        .workflow-node.collaborative-node {
            border-color: #198754;
            background: #e8f5e8;
        }
        .node-sequence {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        .main-sequence {
            background: #0d6efd;
        }
        .collaborative-sequence {
            background: #198754;
        }
        .workflow-flow {
            position: relative;
        }
        .flow-arrow {
            text-align: center;
            color: #6c757d;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-sitemap me-2"></i>
                        工作流详情
                    </h2>
                    <div>
                        <a href="/manage/workflows/<?= $workflow['id'] ?>/edit" class="btn btn-primary me-2">
                            <i class="fas fa-edit me-1"></i>编辑
                        </a>
                        <a href="/manage/nodes?workflow_id=<?= $workflow['id'] ?>" class="btn btn-success me-2">
                            <i class="fas fa-project-diagram me-1"></i>管理节点
                        </a>
                        <a href="/manage/workflows" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回列表
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- 基本信息 -->
                    <div class="col-lg-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">基本信息</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="text-muted">工作流名称：</td>
                                        <td><strong><?= esc($workflow['name']) ?></strong></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">工作流编码：</td>
                                        <td><code><?= esc($workflow['code']) ?></code></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">产品线：</td>
                                        <td><span class="badge bg-info">产品线<?= $workflow['product_line'] ?></span></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">版本号：</td>
                                        <td><?= esc($workflow['version']) ?></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">状态：</td>
                                        <td>
                                            <?php if ($workflow['is_active']): ?>
                                                <span class="badge bg-success">激活</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">停用</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">创建时间：</td>
                                        <td><?= date('Y-m-d H:i:s', strtotime($workflow['created_at'])) ?></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">更新时间：</td>
                                        <td><?= date('Y-m-d H:i:s', strtotime($workflow['updated_at'])) ?></td>
                                    </tr>
                                </table>

                                <?php if ($workflow['description']): ?>
                                    <hr>
                                    <h6>描述</h6>
                                    <p class="text-muted"><?= nl2br(esc($workflow['description'])) ?></p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- 统计信息 -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">统计信息</h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border rounded p-3 mb-3">
                                            <div class="h4 mb-0 text-primary"><?= $workflow['node_count'] ?></div>
                                            <small class="text-muted">节点数量</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="border rounded p-3 mb-3">
                                            <div class="h4 mb-0 text-warning"><?= $workflow['project_count'] ?></div>
                                            <small class="text-muted">项目数量</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 工作流程图 -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">工作流程图</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($workflow['nodes'])): ?>
                                    <div class="text-center py-5">
                                        <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">该工作流暂无节点定义</p>
                                        <a href="/manage/nodes/create?workflow_id=<?= $workflow['id'] ?>" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>添加第一个节点
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="workflow-flow">
                                        <?php foreach ($workflow['nodes'] as $index => $node): ?>
                                            <div class="workflow-node <?= $node['node_type'] === 'main' ? 'main-node' : 'collaborative-node' ?>">
                                                <div class="d-flex align-items-start">
                                                    <div class="node-sequence <?= $node['node_type'] === 'main' ? 'main-sequence' : 'collaborative-sequence' ?> me-3">
                                                        <?= $node['sequence'] ?>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <h6 class="mb-1">
                                                                    <?= esc($node['node_name']) ?>
                                                                    <span class="badge bg-<?= $node['node_type'] === 'main' ? 'primary' : 'success' ?> ms-2">
                                                                        <?= $node['node_type'] === 'main' ? '主节点' : '协同节点' ?>
                                                                    </span>
                                                                </h6>
                                                                <p class="text-muted mb-2">
                                                                    <small>
                                                                        <i class="fas fa-code me-1"></i><?= esc($node['node_code']) ?>
                                                                        <i class="fas fa-user ms-3 me-1"></i><?= getAssigneeTypeLabel($node['assignee_type']) ?>
                                                                    </small>
                                                                </p>
                                                            </div>
                                                            <div class="text-end">
                                                                <?php if ($node['can_reject']): ?>
                                                                    <span class="badge bg-warning text-dark">可驳回</span>
                                                                <?php endif; ?>
                                                                <?php if ($node['auto_assign']): ?>
                                                                    <span class="badge bg-info">自动分配</span>
                                                                <?php endif; ?>
                                                                <?php if ($node['is_parallel']): ?>
                                                                    <span class="badge bg-secondary">并行</span>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        
                                                        <?php if (!empty($node['action_required'])): ?>
                                                            <div class="mt-2">
                                                                <small class="text-muted">操作要求：</small>
                                                                <div class="small">
                                                                    <?php 
                                                                    $actions = is_array($node['action_required']) 
                                                                        ? $node['action_required'] 
                                                                        : json_decode($node['action_required'], true);
                                                                    if ($actions):
                                                                        foreach ($actions as $action):
                                                                    ?>
                                                                        <span class="badge bg-light text-dark me-1"><?= esc($action) ?></span>
                                                                    <?php 
                                                                        endforeach;
                                                                    endif;
                                                                    ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if ($node['can_reject'] && $node['reject_to_node']): ?>
                                                            <div class="mt-2">
                                                                <small class="text-muted">
                                                                    <i class="fas fa-undo me-1"></i>
                                                                    驳回到：<code><?= esc($node['reject_to_node']) ?></code>
                                                                </small>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <?php if ($index < count($workflow['nodes']) - 1): ?>
                                                <div class="flow-arrow">
                                                    <i class="fas fa-arrow-down fa-2x"></i>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * 获取处理人类型标签
 * @param string $assigneeType
 * @return string
 */
function getAssigneeTypeLabel(string $assigneeType): string
{
    $labels = [
        'sales' => '销售人员',
        'main_manager' => '主部门负责人',
        'main_executor' => '主部门执行人员',
        'collab_manager' => '协同部门负责人',
        'collab_executor' => '协同部门执行人员',
        'collab_analyst' => '协同部门数据分析师',
        'business' => '商务负责人'
    ];
    
    return $labels[$assigneeType] ?? $assigneeType;
}
?>
