<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作流定义管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-sitemap me-2"></i>工作流定义管理</h2>
                    <a href="/manage/workflows/create" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>新增工作流
                    </a>
                </div>

                <!-- 筛选条件 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">产品线</label>
                                <select name="product_line" class="form-select">
                                    <option value="">全部产品线</option>
                                    <?php foreach ($productLines as $line): ?>
                                        <option value="<?= $line ?>" <?= $filters['product_line'] === $line ? 'selected' : '' ?>>
                                            产品线<?= $line ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">状态</label>
                                <select name="is_active" class="form-select">
                                    <option value="">全部状态</option>
                                    <option value="1" <?= $filters['is_active'] === '1' ? 'selected' : '' ?>>激活</option>
                                    <option value="0" <?= $filters['is_active'] === '0' ? 'selected' : '' ?>>停用</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">关键词</label>
                                <input type="text" name="keyword" class="form-control" 
                                       placeholder="搜索名称、编码或描述" value="<?= esc($filters['keyword']) ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-1"></i>搜索
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 工作流列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">工作流列表 (共 <?= $total ?> 条)</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($workflows)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">暂无工作流定义</p>
                                <a href="/manage/workflows/create" class="btn btn-primary">创建第一个工作流</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped mb-0 align-middle">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>ID</th>
                                            <th>工作流名称</th>
                                            <th>编码</th>
                                            <th>产品线</th>
                                            <th>版本</th>
                                            <th>状态</th>
                                            <th>节点数</th>
                                            <th>项目数</th>
                                            <th>创建时间</th>
                                            <th width="200">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($workflows as $workflow): ?>
                                            <tr>
                                                <td><?= $workflow['id'] ?></td>
                                                <td>
                                                    <strong><?= esc($workflow['name']) ?></strong>
                                                    <?php if ($workflow['description']): ?>
                                                        <br><small class="text-muted"><?= esc($workflow['description']) ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><code><?= esc($workflow['code']) ?></code></td>
                                                <td>
                                                    <span class="badge bg-info">产品线<?= $workflow['product_line'] ?></span>
                                                </td>
                                                <td><?= esc($workflow['version']) ?></td>
                                                <td>
                                                    <?php if ($workflow['is_active']): ?>
                                                        <span class="badge bg-success">激活</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">停用</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary"><?= $workflow['node_count'] ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-warning text-dark"><?= $workflow['project_count'] ?></span>
                                                </td>
                                                <td><?= date('Y-m-d H:i', strtotime($workflow['created_at'])) ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="/manage/workflows/<?= $workflow['id'] ?>"
                                                           class="btn btn-outline-info" title="查看详情">
                                                            查看
                                                        </a>
                                                        <a href="/manage/workflows/<?= $workflow['id'] ?>/edit"
                                                           class="btn btn-outline-primary" title="编辑">
                                                            编辑
                                                        </a>
                                                        <a href="/manage/nodes?workflow_id=<?= $workflow['id'] ?>"
                                                           class="btn btn-outline-secondary" title="管理节点">
                                                            节点
                                                        </a>
                                                        <?php if ($workflow['is_active']): ?>
                                                            <button type="button" class="btn btn-outline-warning"
                                                                    onclick="toggleStatus(<?= $workflow['id'] ?>, 'deactivate')" title="停用">
                                                                停用
                                                            </button>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-outline-success"
                                                                    onclick="toggleStatus(<?= $workflow['id'] ?>, 'activate')" title="激活">
                                                                激活
                                                            </button>
                                                        <?php endif; ?>
                                                        <button type="button" class="btn btn-outline-info"
                                                                onclick="copyWorkflow(<?= $workflow['id'] ?>)" title="复制">
                                                            复制
                                                        </button>
                                                        <?php if ($workflow['project_count'] == 0): ?>
                                                            <button type="button" class="btn btn-outline-danger"
                                                                    onclick="deleteWorkflow(<?= $workflow['id'] ?>)" title="删除">
                                                                删除
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <?php if ($pager): ?>
                                <div class="card-footer">
                                    <?= $pager->links() ?>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.all.min.js"></script>
    <script>
        /**
         * 切换工作流状态
         * @param {number} id 工作流ID
         * @param {string} action 操作类型 activate|deactivate
         */
        function toggleStatus(id, action) {
            const actionText = action === 'activate' ? '激活' : '停用';
            
            Swal.fire({
                title: `确认${actionText}工作流？`,
                text: `此操作将${actionText}该工作流`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: `确认${actionText}`,
                cancelButtonText: '取消'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/manage/workflows/${id}/${action}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire('成功', data.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('错误', data.message, 'error');
                        }
                    })
                    .catch(error => {
                        Swal.fire('错误', '操作失败，请重试', 'error');
                    });
                }
            });
        }

        /**
         * 复制工作流
         * @param {number} id 工作流ID
         */
        function copyWorkflow(id) {
            Swal.fire({
                title: '确认复制工作流？',
                text: '将创建该工作流的副本',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: '确认复制',
                cancelButtonText: '取消'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/manage/workflows/${id}/copy`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire('成功', data.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('错误', data.message, 'error');
                        }
                    })
                    .catch(error => {
                        Swal.fire('错误', '复制失败，请重试', 'error');
                    });
                }
            });
        }

        /**
         * 删除工作流
         * @param {number} id 工作流ID
         */
        function deleteWorkflow(id) {
            Swal.fire({
                title: '确认删除工作流？',
                text: '此操作不可恢复！',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '确认删除',
                cancelButtonText: '取消',
                confirmButtonColor: '#dc3545'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/manage/workflows/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire('成功', data.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('错误', data.message, 'error');
                        }
                    })
                    .catch(error => {
                        Swal.fire('错误', '删除失败，请重试', 'error');
                    });
                }
            });
        }

        // 显示成功/错误消息
        <?php if (session('success')): ?>
            Swal.fire('成功', '<?= session('success') ?>', 'success');
        <?php endif; ?>

        <?php if (session('error')): ?>
            Swal.fire('错误', '<?= session('error') ?>', 'error');
        <?php endif; ?>
    </script>
</body>
</html>
