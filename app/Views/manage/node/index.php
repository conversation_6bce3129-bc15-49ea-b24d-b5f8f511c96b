<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点定义管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-project-diagram me-2"></i>节点定义管理</h2>
                    <div>
                        <a href="/manage/nodes/create<?= !empty($filters['workflow_id']) ? '?workflow_id=' . $filters['workflow_id'] : '' ?>" 
                           class="btn btn-primary me-2">
                            <i class="fas fa-plus me-1"></i>新增节点
                        </a>
                        <a href="/manage/workflows" class="btn btn-outline-secondary">
                            <i class="fas fa-sitemap me-1"></i>工作流管理
                        </a>
                    </div>
                </div>

                <!-- 筛选条件 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">工作流</label>
                                <select name="workflow_id" class="form-select">
                                    <option value="">全部工作流</option>
                                    <?php foreach ($workflows as $workflow): ?>
                                        <option value="<?= $workflow['id'] ?>" 
                                                <?= $filters['workflow_id'] == $workflow['id'] ? 'selected' : '' ?>>
                                            <?= esc($workflow['name']) ?> (<?= $workflow['product_line'] ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">节点类型</label>
                                <select name="node_type" class="form-select">
                                    <option value="">全部类型</option>
                                    <?php foreach ($nodeTypes as $value => $label): ?>
                                        <option value="<?= $value ?>" <?= $filters['node_type'] === $value ? 'selected' : '' ?>>
                                            <?= $label ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">处理人类型</label>
                                <select name="assignee_type" class="form-select">
                                    <option value="">全部类型</option>
                                    <?php foreach ($assigneeTypes as $value => $label): ?>
                                        <option value="<?= $value ?>" <?= $filters['assignee_type'] === $value ? 'selected' : '' ?>>
                                            <?= $label ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">关键词</label>
                                <input type="text" name="keyword" class="form-control" 
                                       placeholder="搜索节点名称或编码" value="<?= esc($filters['keyword']) ?>">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 节点列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">节点列表 (共 <?= $total ?> 条)</h5>
                        <?php if (!empty($filters['workflow_id'])): ?>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="enableSorting()">
                                <i class="fas fa-sort me-1"></i>排序模式
                            </button>
                        <?php endif; ?>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($nodes)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                                <p class="text-muted">暂无节点定义</p>
                                <a href="/manage/nodes/create<?= !empty($filters['workflow_id']) ? '?workflow_id=' . $filters['workflow_id'] : '' ?>" 
                                   class="btn btn-primary">创建第一个节点</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped mb-0 align-middle" id="nodesTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th width="50">序号</th>
                                            <th>节点名称</th>
                                            <th>节点编码</th>
                                            <th>工作流</th>
                                            <th>类型</th>
                                            <th>处理人类型</th>
                                            <th>特性</th>
                                            <th width="200">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sortableNodes">
                                        <?php foreach ($nodes as $node): ?>
                                            <tr data-id="<?= $node['id'] ?>" data-sequence="<?= $node['sequence'] ?>">
                                                <td>
                                                    <span class="badge bg-<?= $node['node_type'] === 'main' ? 'primary' : 'success' ?> fs-6">
                                                        <?= $node['sequence'] ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong><?= esc($node['node_name']) ?></strong>
                                                </td>
                                                <td><code><?= esc($node['node_code']) ?></code></td>
                                                <td>
                                                    <small class="text-muted"><?= esc($node['workflow_name']) ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?= $node['node_type'] === 'main' ? 'primary' : 'success' ?>">
                                                        <?= $nodeTypes[$node['node_type']] ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small><?= $assigneeTypes[$node['assignee_type']] ?></small>
                                                </td>
                                                <td>
                                                    <?php if ($node['can_reject']): ?>
                                                        <span class="badge bg-warning text-dark">可驳回</span>
                                                    <?php endif; ?>
                                                    <?php if ($node['auto_assign']): ?>
                                                        <span class="badge bg-info">自动分配</span>
                                                    <?php endif; ?>
                                                    <?php if ($node['is_parallel']): ?>
                                                        <span class="badge bg-secondary">并行</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="/manage/nodes/<?= $node['id'] ?>" 
                                                           class="btn btn-outline-info" title="查看详情">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="/manage/nodes/<?= $node['id'] ?>/edit" 
                                                           class="btn btn-outline-primary" title="编辑">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-outline-danger" 
                                                                onclick="deleteNode(<?= $node['id'] ?>)" title="删除">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <?php if ($pager): ?>
                                <div class="card-footer">
                                    <?= $pager->links() ?>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        let sortable = null;
        let sortingEnabled = false;

        /**
         * 启用排序模式
         */
        function enableSorting() {
            if (sortingEnabled) {
                disableSorting();
                return;
            }

            sortingEnabled = true;
            const tbody = document.getElementById('sortableNodes');
            
            sortable = Sortable.create(tbody, {
                animation: 150,
                ghostClass: 'table-warning',
                onEnd: function(evt) {
                    saveSortOrder();
                }
            });

            // 更新按钮状态
            const btn = event.target.closest('button');
            btn.innerHTML = '<i class="fas fa-save me-1"></i>保存排序';
            btn.className = 'btn btn-sm btn-success';

            Swal.fire({
                title: '排序模式已启用',
                text: '拖拽行来重新排序，完成后点击保存',
                icon: 'info',
                timer: 2000,
                showConfirmButton: false
            });
        }

        /**
         * 禁用排序模式
         */
        function disableSorting() {
            if (sortable) {
                sortable.destroy();
                sortable = null;
            }
            sortingEnabled = false;

            // 恢复按钮状态
            const btn = document.querySelector('.btn-success');
            if (btn) {
                btn.innerHTML = '<i class="fas fa-sort me-1"></i>排序模式';
                btn.className = 'btn btn-sm btn-outline-info';
            }
        }

        /**
         * 保存排序
         */
        function saveSortOrder() {
            const rows = document.querySelectorAll('#sortableNodes tr');
            const sortData = [];

            rows.forEach((row, index) => {
                sortData.push({
                    id: parseInt(row.dataset.id),
                    sequence: index + 1
                });
            });

            fetch('/manage/nodes/sort', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(sortData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('成功', '节点排序已保存', 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('错误', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('错误', '保存排序失败，请重试', 'error');
            });
        }

        /**
         * 删除节点
         * @param {number} id 节点ID
         */
        function deleteNode(id) {
            Swal.fire({
                title: '确认删除节点？',
                text: '此操作不可恢复！',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '确认删除',
                cancelButtonText: '取消',
                confirmButtonColor: '#dc3545'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/manage/nodes/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire('成功', data.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('错误', data.message, 'error');
                        }
                    })
                    .catch(error => {
                        Swal.fire('错误', '删除失败，请重试', 'error');
                    });
                }
            });
        }

        // 显示成功/错误消息
        <?php if (session('success')): ?>
            Swal.fire('成功', '<?= session('success') ?>', 'success');
        <?php endif; ?>

        <?php if (session('error')): ?>
            Swal.fire('错误', '<?= session('error') ?>', 'error');
        <?php endif; ?>
    </script>
</body>
</html>
