<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $node ? '编辑节点' : '新增节点' ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-project-diagram me-2"></i>
                        <?= $node ? '编辑节点' : '新增节点' ?>
                    </h2>
                    <a href="/manage/nodes<?= $selectedWorkflowId ? '?workflow_id=' . $selectedWorkflowId : '' ?>" 
                       class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">节点信息</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="<?= $node ? '/manage/nodes/' . $node['id'] : '/manage/nodes' ?>" id="nodeForm">
                                    <?php if ($node): ?>
                                        <input type="hidden" name="_method" value="PUT">
                                    <?php endif; ?>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="workflow_id" class="form-label">所属工作流 <span class="text-danger">*</span></label>
                                                <select class="form-select" id="workflow_id" name="workflow_id" required>
                                                    <option value="">请选择工作流</option>
                                                    <?php foreach ($workflows as $workflow): ?>
                                                        <option value="<?= $workflow['id'] ?>" 
                                                                <?= old('workflow_id', $selectedWorkflowId ?: ($node['workflow_id'] ?? '')) == $workflow['id'] ? 'selected' : '' ?>>
                                                            <?= esc($workflow['name']) ?> (<?= $workflow['product_line'] ?>)
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="sequence" class="form-label">节点序号 <span class="text-danger">*</span></label>
                                                <input type="number" class="form-control" id="sequence" name="sequence" 
                                                       value="<?= old('sequence', $node['sequence'] ?? '') ?>" min="1" required>
                                                <div class="form-text">节点在工作流中的执行顺序</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="node_code" class="form-label">节点编码 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="node_code" name="node_code" 
                                                       value="<?= old('node_code', $node['node_code'] ?? '') ?>" required>
                                                <div class="form-text">唯一标识符，建议使用英文字母和下划线</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="node_name" class="form-label">节点名称 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="node_name" name="node_name" 
                                                       value="<?= old('node_name', $node['node_name'] ?? '') ?>" required>
                                                <div class="form-text">节点的显示名称</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="node_type" class="form-label">节点类型 <span class="text-danger">*</span></label>
                                                <select class="form-select" id="node_type" name="node_type" required>
                                                    <option value="">请选择节点类型</option>
                                                    <?php foreach ($nodeTypes as $value => $label): ?>
                                                        <option value="<?= $value ?>" 
                                                                <?= old('node_type', $node['node_type'] ?? '') === $value ? 'selected' : '' ?>>
                                                            <?= $label ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="assignee_type" class="form-label">处理人类型 <span class="text-danger">*</span></label>
                                                <select class="form-select" id="assignee_type" name="assignee_type" required>
                                                    <option value="">请选择处理人类型</option>
                                                    <?php foreach ($assigneeTypes as $value => $label): ?>
                                                        <option value="<?= $value ?>" 
                                                                <?= old('assignee_type', $node['assignee_type'] ?? '') === $value ? 'selected' : '' ?>>
                                                            <?= $label ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="action_required" class="form-label">操作要求 <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="action_required" name="action_required" rows="3" required><?= old('action_required', is_array($node['action_required'] ?? '') ? implode("\n", $node['action_required']) : ($node['action_required'] ?? '')) ?></textarea>
                                        <div class="form-text">每行一个操作要求，系统会自动转换为JSON格式</div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="can_reject" name="can_reject" value="1"
                                                           <?= old('can_reject', $node['can_reject'] ?? 0) ? 'checked' : '' ?>>
                                                    <label class="form-check-label" for="can_reject">
                                                        允许驳回
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3" id="reject_to_node_group" style="display: none;">
                                                <label for="reject_to_node" class="form-label">驳回到节点</label>
                                                <input type="text" class="form-control" id="reject_to_node" name="reject_to_node" 
                                                       value="<?= old('reject_to_node', $node['reject_to_node'] ?? '') ?>">
                                                <div class="form-text">驳回时跳转到的节点编码</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="auto_assign" name="auto_assign" value="1"
                                                           <?= old('auto_assign', $node['auto_assign'] ?? 0) ? 'checked' : '' ?>>
                                                    <label class="form-check-label" for="auto_assign">
                                                        自动分配处理人
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="is_parallel" name="is_parallel" value="1"
                                                           <?= old('is_parallel', $node['is_parallel'] ?? 0) ? 'checked' : '' ?>>
                                                    <label class="form-check-label" for="is_parallel">
                                                        并行节点
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="conditions" class="form-label">执行条件</label>
                                        <textarea class="form-control" id="conditions" name="conditions" rows="2"><?= old('conditions', is_array($node['conditions'] ?? '') ? json_encode($node['conditions'], JSON_UNESCAPED_UNICODE) : ($node['conditions'] ?? '')) ?></textarea>
                                        <div class="form-text">JSON格式的执行条件（可选）</div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <a href="/manage/nodes<?= $selectedWorkflowId ? '?workflow_id=' . $selectedWorkflowId : '' ?>" 
                                           class="btn btn-secondary me-2">取消</a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>
                                            <?= $node ? '更新' : '创建' ?>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">帮助信息</h5>
                            </div>
                            <div class="card-body">
                                <h6><i class="fas fa-info-circle text-info me-1"></i>字段说明</h6>
                                <ul class="list-unstyled small">
                                    <li><strong>节点编码：</strong>在工作流中的唯一标识</li>
                                    <li><strong>节点序号：</strong>执行顺序，数字越小越先执行</li>
                                    <li><strong>节点类型：</strong>主节点或协同节点</li>
                                    <li><strong>处理人类型：</strong>谁来处理这个节点</li>
                                    <li><strong>操作要求：</strong>处理人需要完成的操作</li>
                                </ul>

                                <hr>

                                <h6><i class="fas fa-lightbulb text-warning me-1"></i>注意事项</h6>
                                <ul class="list-unstyled small">
                                    <li>• 节点编码在同一工作流中必须唯一</li>
                                    <li>• 序号决定执行顺序，可以不连续</li>
                                    <li>• 协同节点通常用于并行处理</li>
                                    <li>• 驳回功能需要指定目标节点</li>
                                </ul>

                                <hr>

                                <h6><i class="fas fa-cogs text-secondary me-1"></i>高级选项</h6>
                                <ul class="list-unstyled small">
                                    <li><strong>自动分配：</strong>系统自动分配处理人</li>
                                    <li><strong>并行节点：</strong>可以同时执行多个</li>
                                    <li><strong>执行条件：</strong>满足条件才执行</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.all.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 驳回选项显示/隐藏
            $('#can_reject').change(function() {
                if ($(this).is(':checked')) {
                    $('#reject_to_node_group').show();
                    $('#reject_to_node').attr('required', true);
                } else {
                    $('#reject_to_node_group').hide();
                    $('#reject_to_node').attr('required', false);
                }
            }).trigger('change');

            // 表单验证
            $('#nodeForm').on('submit', function(e) {
                const workflowId = $('#workflow_id').val();
                const nodeCode = $('#node_code').val().trim();
                const nodeName = $('#node_name').val().trim();
                const nodeType = $('#node_type').val();
                const assigneeType = $('#assignee_type').val();
                const actionRequired = $('#action_required').val().trim();
                const sequence = $('#sequence').val();

                if (!workflowId || !nodeCode || !nodeName || !nodeType || !assigneeType || !actionRequired || !sequence) {
                    e.preventDefault();
                    Swal.fire('错误', '请填写所有必填字段', 'error');
                    return false;
                }

                // 验证编码格式
                const codePattern = /^[a-zA-Z][a-zA-Z0-9_]*$/;
                if (!codePattern.test(nodeCode)) {
                    e.preventDefault();
                    Swal.fire('错误', '节点编码必须以字母开头，只能包含字母、数字和下划线', 'error');
                    return false;
                }

                // 验证序号
                if (sequence < 1) {
                    e.preventDefault();
                    Swal.fire('错误', '节点序号必须大于0', 'error');
                    return false;
                }

                // 处理操作要求
                const actions = actionRequired.split('\n').filter(action => action.trim());
                if (actions.length === 0) {
                    e.preventDefault();
                    Swal.fire('错误', '至少需要一个操作要求', 'error');
                    return false;
                }

                // 将操作要求转换为数组
                $('<input>').attr({
                    type: 'hidden',
                    name: 'action_required',
                    value: JSON.stringify(actions)
                }).appendTo(this);
                $('#action_required').remove();

                // 验证条件JSON格式
                const conditions = $('#conditions').val().trim();
                if (conditions) {
                    try {
                        JSON.parse(conditions);
                    } catch (error) {
                        e.preventDefault();
                        Swal.fire('错误', '执行条件必须是有效的JSON格式', 'error');
                        return false;
                    }
                }
            });

            // 自动生成编码
            $('#node_name').on('blur', function() {
                const name = $(this).val().trim();
                const code = $('#node_code').val().trim();
                
                if (name && !code) {
                    const generatedCode = name.toLowerCase()
                        .replace(/\s+/g, '_')
                        .replace(/[^a-z0-9_]/g, '');
                    $('#node_code').val(generatedCode);
                }
            });
        });

        // 显示错误消息
        <?php if (session('errors')): ?>
            const errors = <?= json_encode(session('errors')) ?>;
            let errorMessage = '请修正以下错误：\n';
            for (const field in errors) {
                errorMessage += '• ' + errors[field] + '\n';
            }
            Swal.fire('验证错误', errorMessage, 'error');
        <?php endif; ?>

        <?php if (session('error')): ?>
            Swal.fire('错误', '<?= session('error') ?>', 'error');
        <?php endif; ?>
    </script>
</body>
</html>
