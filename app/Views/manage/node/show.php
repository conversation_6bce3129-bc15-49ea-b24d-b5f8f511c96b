<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点详情 - <?= esc($node['node_name']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-project-diagram me-2"></i>
                        节点详情
                    </h2>
                    <div>
                        <a href="/manage/nodes/<?= $node['id'] ?>/edit" class="btn btn-primary me-2">
                            <i class="fas fa-edit me-1"></i>编辑
                        </a>
                        <a href="/manage/nodes?workflow_id=<?= $node['workflow_id'] ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回列表
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- 基本信息 -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">基本信息</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="text-muted" width="120">节点名称：</td>
                                        <td><strong><?= esc($node['node_name']) ?></strong></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">节点编码：</td>
                                        <td><code><?= esc($node['node_code']) ?></code></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">所属工作流：</td>
                                        <td>
                                            <a href="/manage/workflows/<?= $node['workflow_id'] ?>" class="text-decoration-none">
                                                <?= esc($node['workflow_name']) ?>
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">节点序号：</td>
                                        <td>
                                            <span class="badge bg-<?= $node['node_type'] === 'main' ? 'primary' : 'success' ?> fs-6">
                                                <?= $node['sequence'] ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">节点类型：</td>
                                        <td>
                                            <span class="badge bg-<?= $node['node_type'] === 'main' ? 'primary' : 'success' ?>">
                                                <?= $node['node_type'] === 'main' ? '主节点' : '协同节点' ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">处理人类型：</td>
                                        <td><?= getAssigneeTypeLabel($node['assignee_type']) ?></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">创建时间：</td>
                                        <td><?= date('Y-m-d H:i:s', strtotime($node['created_at'])) ?></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">更新时间：</td>
                                        <td><?= date('Y-m-d H:i:s', strtotime($node['updated_at'])) ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- 特性配置 -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">特性配置</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-<?= $node['can_reject'] ? 'check-circle text-success' : 'times-circle text-muted' ?> me-2"></i>
                                            <span>允许驳回</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-<?= $node['auto_assign'] ? 'check-circle text-success' : 'times-circle text-muted' ?> me-2"></i>
                                            <span>自动分配</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-<?= $node['is_parallel'] ? 'check-circle text-success' : 'times-circle text-muted' ?> me-2"></i>
                                            <span>并行节点</span>
                                        </div>
                                    </div>
                                </div>

                                <?php if ($node['can_reject'] && $node['reject_to_node']): ?>
                                    <hr>
                                    <div class="alert alert-info">
                                        <i class="fas fa-undo me-2"></i>
                                        <strong>驳回目标：</strong><code><?= esc($node['reject_to_node']) ?></code>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- 操作要求和条件 -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">操作要求</h5>
                            </div>
                            <div class="card-body">
                                <?php 
                                $actions = is_array($node['action_required']) 
                                    ? $node['action_required'] 
                                    : json_decode($node['action_required'], true);
                                ?>
                                <?php if (!empty($actions)): ?>
                                    <ul class="list-group list-group-flush">
                                        <?php foreach ($actions as $index => $action): ?>
                                            <li class="list-group-item d-flex align-items-center">
                                                <span class="badge bg-primary me-2"><?= $index + 1 ?></span>
                                                <?= esc($action) ?>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php else: ?>
                                    <p class="text-muted">暂无操作要求</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php if (!empty($node['conditions'])): ?>
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">执行条件</h5>
                                </div>
                                <div class="card-body">
                                    <pre class="bg-light p-3 rounded"><code><?= esc(is_array($node['conditions']) ? json_encode($node['conditions'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : $node['conditions']) ?></code></pre>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * 获取处理人类型标签
 * @param string $assigneeType
 * @return string
 */
function getAssigneeTypeLabel(string $assigneeType): string
{
    $labels = [
        'sales' => '销售人员',
        'main_manager' => '主部门负责人',
        'main_executor' => '主部门执行人员',
        'collab_manager' => '协同部门负责人',
        'collab_executor' => '协同部门执行人员',
        'collab_analyst' => '协同部门数据分析师',
        'business' => '商务负责人'
    ];
    
    return $labels[$assigneeType] ?? $assigneeType;
}
?>
