<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * 项目模型
 * 管理项目的基本信息和状态
 */
class ProjectModel extends Model
{
    protected $table = 'projects';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'project_no', 'title', 'product_line', 'contract_amount',
        'customer_name', 'customer_contact', 'customer_phone', 'customer_email',
        'signing_status', 'main_department_id', 'sales_user_id', 'business_user_id',
        'status', 'description', 'completed_at'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'project_no' => 'required|max_length[50]|is_unique[projects.project_no,id,{id}]',
        'title' => 'required|max_length[200]',
        'product_line' => 'required|in_list[A,B,C,D]',
        'customer_name' => 'required|max_length[200]',
        'sales_user_id' => 'required|integer',
        'signing_status' => 'in_list[pending,won,lost]',
        'status' => 'in_list[draft,active,completed,cancelled]'
    ];
    
    protected $validationMessages = [
        'project_no' => [
            'required' => '项目编号不能为空',
            'is_unique' => '项目编号已存在'
        ],
        'title' => [
            'required' => '项目标题不能为空'
        ]
    ];
    
    /**
     * 获取项目概览信息
     * 
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array
     */
    public function getProjectOverview($filters = [], $page = 1, $perPage = 20)
    {
        $builder = $this->db->table('v_project_overview');
        
        // 应用筛选条件
        if (!empty($filters['product_line'])) {
            $builder->where('product_line', $filters['product_line']);
        }
        
        if (!empty($filters['status'])) {
            $builder->where('status', $filters['status']);
        }
        
        if (!empty($filters['signing_status'])) {
            $builder->where('signing_status', $filters['signing_status']);
        }
        
        if (!empty($filters['sales_user_id'])) {
            $builder->where('sales_user_id', $filters['sales_user_id']);
        }
        
        if (!empty($filters['date_range'])) {
            $builder->where('created_at >=', $filters['date_range']['start']);
            $builder->where('created_at <=', $filters['date_range']['end']);
        }
        
        // 分页
        $offset = ($page - 1) * $perPage;
        $total = $builder->countAllResults(false);
        $projects = $builder->limit($perPage, $offset)
                           ->orderBy('created_at', 'DESC')
                           ->get()
                           ->getResultArray();
        
        return [
            'data' => $projects,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 根据产品线自动分配主部门
     * 
     * @param string $productLine 产品线
     * @return int|null 部门ID
     */
    public function getMainDepartmentByProductLine($productLine)
    {
        $departmentModel = new DepartmentModel();
        
        // D产品线需要手动选择，返回null
        if ($productLine === 'D') {
            return null;
        }
        
        // 其他产品线自动分配
        $department = $departmentModel->where('type', 'main')
                                    ->where("JSON_CONTAINS(product_lines, '\"$productLine\"')")
                                    ->where('status', 1)
                                    ->first();
        
        return $department ? $department['id'] : null;
    }
    
    /**
     * 生成项目编号
     * 
     * @param string $productLine 产品线
     * @return string
     */
    public function generateProjectNo($productLine)
    {
        $prefix = 'PRJ' . $productLine;
        $date = date('Ymd');
        
        // 查询当天最大序号
        $lastProject = $this->where("project_no LIKE '{$prefix}{$date}%'")
                           ->orderBy('project_no', 'DESC')
                           ->first();
        
        if ($lastProject) {
            $lastNo = substr($lastProject['project_no'], -3);
            $nextNo = str_pad((int)$lastNo + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $nextNo = '001';
        }
        
        return $prefix . $date . $nextNo;
    }
}

/**
 * 项目工作流模型
 * 管理项目的工作流执行状态
 */
class ProjectWorkflowModel extends Model
{
    protected $table = 'project_workflows';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'project_id', 'workflow_definition_id', 'current_node_code',
        'status', 'started_at', 'completed_at'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    /**
     * 创建项目工作流实例
     * 
     * @param int $projectId 项目ID
     * @param string $productLine 产品线
     * @return bool
     */
    public function createProjectWorkflow($projectId, $productLine)
    {
        // 获取工作流定义
        $workflowModel = new WorkflowDefinitionModel();
        $workflow = $workflowModel->where('product_line', $productLine)
                                 ->where('is_active', 1)
                                 ->first();
        
        if (!$workflow) {
            return false;
        }
        
        // 创建工作流实例
        $data = [
            'project_id' => $projectId,
            'workflow_definition_id' => $workflow['id'],
            'status' => 'pending'
        ];
        
        $workflowId = $this->insert($data);
        
        if ($workflowId) {
            // 创建第一个节点实例
            $nodeModel = new ProjectNodeModel();
            return $nodeModel->createFirstNode($workflowId, $workflow['id']);
        }
        
        return false;
    }
    
    /**
     * 流转到下一个节点
     * 
     * @param int $workflowId 工作流实例ID
     * @param string $nextNodeCode 下一个节点编码
     * @return bool
     */
    public function moveToNextNode($workflowId, $nextNodeCode)
    {
        return $this->update($workflowId, [
            'current_node_code' => $nextNodeCode,
            'status' => 'running'
        ]);
    }
    
    /**
     * 完成工作流
     * 
     * @param int $workflowId 工作流实例ID
     * @return bool
     */
    public function completeWorkflow($workflowId)
    {
        return $this->update($workflowId, [
            'status' => 'completed',
            'completed_at' => date('Y-m-d H:i:s')
        ]);
    }
}

/**
 * 项目节点模型
 * 管理项目节点的执行状态
 */
class ProjectNodeModel extends Model
{
    protected $table = 'project_nodes';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'project_workflow_id', 'node_definition_id', 'node_code',
        'assignee_id', 'department_id', 'status', 'started_at',
        'completed_at', 'due_date', 'result_data', 'reject_reason'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    /**
     * 获取用户待处理任务
     * 
     * @param int $userId 用户ID
     * @param array $filters 筛选条件
     * @return array
     */
    public function getPendingTasks($userId, $filters = [])
    {
        $builder = $this->db->table('v_pending_tasks');
        $builder->where('assignee_id', $userId);
        
        if (!empty($filters['product_line'])) {
            $builder->where('product_line', $filters['product_line']);
        }
        
        if (!empty($filters['department_id'])) {
            $builder->where('department_id', $filters['department_id']);
        }
        
        return $builder->orderBy('created_at', 'ASC')
                      ->get()
                      ->getResultArray();
    }
    
    /**
     * 创建第一个节点实例
     * 
     * @param int $workflowId 工作流实例ID
     * @param int $workflowDefinitionId 工作流定义ID
     * @return bool
     */
    public function createFirstNode($workflowId, $workflowDefinitionId)
    {
        $nodeDefinitionModel = new NodeDefinitionModel();
        $firstNode = $nodeDefinitionModel->where('workflow_id', $workflowDefinitionId)
                                        ->where('sequence', 1)
                                        ->first();
        
        if (!$firstNode) {
            return false;
        }
        
        $data = [
            'project_workflow_id' => $workflowId,
            'node_definition_id' => $firstNode['id'],
            'node_code' => $firstNode['node_code'],
            'status' => 'pending'
        ];
        
        return $this->insert($data) !== false;
    }
    
    /**
     * 完成节点处理
     * 
     * @param int $nodeId 节点ID
     * @param array $resultData 处理结果数据
     * @param int $assigneeId 处理人ID
     * @return bool
     */
    public function completeNode($nodeId, $resultData, $assigneeId)
    {
        $data = [
            'status' => 'completed',
            'completed_at' => date('Y-m-d H:i:s'),
            'result_data' => json_encode($resultData),
            'assignee_id' => $assigneeId
        ];
        
        return $this->update($nodeId, $data);
    }
    
    /**
     * 驳回节点
     * 
     * @param int $nodeId 节点ID
     * @param string $rejectReason 驳回原因
     * @param int $assigneeId 处理人ID
     * @return bool
     */
    public function rejectNode($nodeId, $rejectReason, $assigneeId)
    {
        $data = [
            'status' => 'rejected',
            'completed_at' => date('Y-m-d H:i:s'),
            'reject_reason' => $rejectReason,
            'assignee_id' => $assigneeId
        ];
        
        return $this->update($nodeId, $data);
    }
}

/**
 * 部门模型
 * 管理部门信息和产品线关联
 */
class DepartmentModel extends Model
{
    protected $table = 'departments';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'name', 'code', 'type', 'product_lines', 'manager_id',
        'parent_id', 'sort_order', 'status', 'description'
    ];
    
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    /**
     * 获取支持指定产品线的协同部门
     * 
     * @param string $productLine 产品线
     * @return array
     */
    public function getCollaborativeDepartments($productLine)
    {
        return $this->where('type', 'collaborative')
                   ->where("JSON_CONTAINS(product_lines, '\"$productLine\"')")
                   ->where('status', 1)
                   ->orderBy('sort_order', 'ASC')
                   ->findAll();
    }
    
    /**
     * 获取部门的负责人
     * 
     * @param int $departmentId 部门ID
     * @return array|null
     */
    public function getDepartmentManager($departmentId)
    {
        $userModel = new UserModel();
        $department = $this->find($departmentId);
        
        if ($department && $department['manager_id']) {
            return $userModel->find($department['manager_id']);
        }
        
        return null;
    }
}
