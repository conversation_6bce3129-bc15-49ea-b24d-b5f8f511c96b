-- =====================================================
-- 项目流程管理系统数据库设计
-- 设计理念：模块化、可扩展、易维护
-- 框架：CodeIgniter 4.6
-- 字符集：utf8mb4_unicode_ci
-- =====================================================

-- 1. 用户管理模块
-- =====================================================

-- 用户表
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1=正常，0=禁用',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 部门表
CREATE TABLE `departments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `code` varchar(50) NOT NULL COMMENT '部门编码',
  `type` enum('main','collaborative') NOT NULL DEFAULT 'collaborative' COMMENT '部门类型：main=主部门，collaborative=协同部门',
  `product_lines` json DEFAULT NULL COMMENT '支持的产品线，JSON格式：["A","B","C","D"]',
  `manager_id` bigint(20) unsigned DEFAULT NULL COMMENT '部门负责人ID',
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '上级部门ID',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1=正常，0=禁用',
  `description` text DEFAULT NULL COMMENT '部门描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_type` (`type`),
  KEY `idx_manager_id` (`manager_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`),
  CONSTRAINT `fk_departments_manager` FOREIGN KEY (`manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_departments_parent` FOREIGN KEY (`parent_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 用户部门关联表
CREATE TABLE `user_departments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `department_id` bigint(20) unsigned NOT NULL COMMENT '部门ID',
  `role` enum('member','manager','executor','analyst') NOT NULL DEFAULT 'member' COMMENT '角色：member=成员，manager=负责人，executor=执行人员，analyst=数据分析师',
  `is_primary` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否主要部门：1=是，0=否',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_dept_role` (`user_id`, `department_id`, `role`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_role` (`role`),
  CONSTRAINT `fk_user_departments_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_departments_dept` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户部门关联表';

-- 2. 项目管理模块
-- =====================================================

-- 项目表
CREATE TABLE `projects` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_no` varchar(50) NOT NULL COMMENT '项目编号',
  `title` varchar(200) NOT NULL COMMENT '项目标题',
  `product_line` enum('A','B','C','D') NOT NULL COMMENT '产品线',
  `contract_amount` decimal(15,2) DEFAULT NULL COMMENT '合同金额',
  `customer_name` varchar(200) NOT NULL COMMENT '客户名称',
  `customer_contact` varchar(100) DEFAULT NULL COMMENT '客户联系人',
  `customer_phone` varchar(20) DEFAULT NULL COMMENT '客户电话',
  `customer_email` varchar(100) DEFAULT NULL COMMENT '客户邮箱',
  `signing_status` enum('pending','won','lost') NOT NULL DEFAULT 'pending' COMMENT '签约状态：pending=待定，won=中标，lost=未中标',
  `main_department_id` bigint(20) unsigned DEFAULT NULL COMMENT '主部门ID',
  `sales_user_id` bigint(20) unsigned NOT NULL COMMENT '销售人员ID',
  `business_user_id` bigint(20) unsigned DEFAULT NULL COMMENT '商务负责人ID',
  `status` enum('draft','active','completed','cancelled') NOT NULL DEFAULT 'draft' COMMENT '项目状态',
  `description` text DEFAULT NULL COMMENT '项目描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_no` (`project_no`),
  KEY `idx_product_line` (`product_line`),
  KEY `idx_signing_status` (`signing_status`),
  KEY `idx_main_department_id` (`main_department_id`),
  KEY `idx_sales_user_id` (`sales_user_id`),
  KEY `idx_business_user_id` (`business_user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  CONSTRAINT `fk_projects_main_dept` FOREIGN KEY (`main_department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_projects_sales_user` FOREIGN KEY (`sales_user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_projects_business_user` FOREIGN KEY (`business_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目表';

-- 3. 工作流引擎模块
-- =====================================================

-- 工作流定义表
CREATE TABLE `workflow_definitions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '工作流定义ID',
  `name` varchar(100) NOT NULL COMMENT '工作流名称',
  `code` varchar(50) NOT NULL COMMENT '工作流编码',
  `product_line` enum('A','B','C','D') NOT NULL COMMENT '适用产品线',
  `version` varchar(20) NOT NULL DEFAULT '1.0' COMMENT '版本号',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活：1=是，0=否',
  `description` text DEFAULT NULL COMMENT '描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code_version` (`code`, `version`),
  KEY `idx_product_line` (`product_line`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流定义表';

-- 节点定义表
CREATE TABLE `node_definitions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '节点定义ID',
  `workflow_id` bigint(20) unsigned NOT NULL COMMENT '工作流ID',
  `node_code` varchar(50) NOT NULL COMMENT '节点编码',
  `node_name` varchar(100) NOT NULL COMMENT '节点名称',
  `node_type` enum('main','collaborative') NOT NULL DEFAULT 'main' COMMENT '节点类型：main=主节点，collaborative=协同节点',
  `sequence` int(11) NOT NULL COMMENT '节点序号',
  `assignee_type` enum('sales','main_manager','main_executor','collab_manager','collab_executor','collab_analyst','business') NOT NULL COMMENT '处理人类型',
  `action_required` json NOT NULL COMMENT '需要执行的操作，JSON格式',
  `can_reject` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可以驳回：1=是，0=否',
  `reject_to_node` varchar(50) DEFAULT NULL COMMENT '驳回到的节点编码',
  `auto_assign` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动分配：1=是，0=否',
  `is_parallel` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否并行节点：1=是，0=否',
  `conditions` json DEFAULT NULL COMMENT '节点条件，JSON格式',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_workflow_node_code` (`workflow_id`, `node_code`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_node_type` (`node_type`),
  KEY `idx_sequence` (`sequence`),
  CONSTRAINT `fk_node_definitions_workflow` FOREIGN KEY (`workflow_id`) REFERENCES `workflow_definitions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='节点定义表';

-- 4. 项目实例模块
-- =====================================================

-- 项目工作流实例表
CREATE TABLE `project_workflows` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '项目工作流实例ID',
  `project_id` bigint(20) unsigned NOT NULL COMMENT '项目ID',
  `workflow_definition_id` bigint(20) unsigned NOT NULL COMMENT '工作流定义ID',
  `current_node_code` varchar(50) DEFAULT NULL COMMENT '当前节点编码',
  `status` enum('pending','running','completed','cancelled','rejected') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_workflow` (`project_id`),
  KEY `idx_workflow_definition_id` (`workflow_definition_id`),
  KEY `idx_current_node_code` (`current_node_code`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_project_workflows_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_project_workflows_definition` FOREIGN KEY (`workflow_definition_id`) REFERENCES `workflow_definitions` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目工作流实例表';

-- 项目节点实例表
CREATE TABLE `project_nodes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '项目节点实例ID',
  `project_workflow_id` bigint(20) unsigned NOT NULL COMMENT '项目工作流实例ID',
  `node_definition_id` bigint(20) unsigned NOT NULL COMMENT '节点定义ID',
  `node_code` varchar(50) NOT NULL COMMENT '节点编码',
  `assignee_id` bigint(20) unsigned DEFAULT NULL COMMENT '处理人ID',
  `department_id` bigint(20) unsigned DEFAULT NULL COMMENT '处理部门ID（协同节点使用）',
  `status` enum('pending','running','completed','rejected','skipped') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `due_date` timestamp NULL DEFAULT NULL COMMENT '截止时间',
  `result_data` json DEFAULT NULL COMMENT '处理结果数据，JSON格式',
  `reject_reason` text DEFAULT NULL COMMENT '驳回原因',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_workflow_id` (`project_workflow_id`),
  KEY `idx_node_definition_id` (`node_definition_id`),
  KEY `idx_node_code` (`node_code`),
  KEY `idx_assignee_id` (`assignee_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_status` (`status`),
  KEY `idx_started_at` (`started_at`),
  CONSTRAINT `fk_project_nodes_workflow` FOREIGN KEY (`project_workflow_id`) REFERENCES `project_workflows` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_project_nodes_definition` FOREIGN KEY (`node_definition_id`) REFERENCES `node_definitions` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_project_nodes_assignee` FOREIGN KEY (`assignee_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_project_nodes_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目节点实例表';

-- 项目协同部门表
CREATE TABLE `project_collaborations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `project_id` bigint(20) unsigned NOT NULL COMMENT '项目ID',
  `department_id` bigint(20) unsigned NOT NULL COMMENT '协同部门ID',
  `collaboration_content` text DEFAULT NULL COMMENT '协作内容',
  `status` enum('pending','active','completed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `assigned_at` timestamp NULL DEFAULT NULL COMMENT '分配时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_department` (`project_id`, `department_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_project_collaborations_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_project_collaborations_dept` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目协同部门表';

-- 5. 文件管理模块
-- =====================================================

-- 项目文件表
CREATE TABLE `project_files` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `project_id` bigint(20) unsigned NOT NULL COMMENT '项目ID',
  `node_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联节点ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `file_category` enum('proposal','data','ppt','document','other') NOT NULL DEFAULT 'other' COMMENT '文件分类',
  `uploaded_by` bigint(20) unsigned NOT NULL COMMENT '上传人ID',
  `description` text DEFAULT NULL COMMENT '文件描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_node_id` (`node_id`),
  KEY `idx_file_category` (`file_category`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  CONSTRAINT `fk_project_files_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_project_files_node` FOREIGN KEY (`node_id`) REFERENCES `project_nodes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_project_files_uploader` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目文件表';

-- 6. 操作日志模块
-- =====================================================

-- 项目操作日志表
CREATE TABLE `project_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `project_id` bigint(20) unsigned NOT NULL COMMENT '项目ID',
  `node_id` bigint(20) unsigned DEFAULT NULL COMMENT '节点ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `action_desc` varchar(200) NOT NULL COMMENT '操作描述',
  `old_data` json DEFAULT NULL COMMENT '操作前数据，JSON格式',
  `new_data` json DEFAULT NULL COMMENT '操作后数据，JSON格式',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_node_id` (`node_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_project_logs_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_project_logs_node` FOREIGN KEY (`node_id`) REFERENCES `project_nodes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_project_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目操作日志表';

-- 7. 系统配置模块
-- =====================================================

-- 系统配置表
CREATE TABLE `system_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `config_group` varchar(50) NOT NULL DEFAULT 'system' COMMENT '配置分组',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `is_public` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否公开：1=是，0=否',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_group` (`config_group`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 8. 通知模块
-- =====================================================

-- 通知表
CREATE TABLE `notifications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '接收用户ID',
  `project_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联项目ID',
  `node_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联节点ID',
  `type` enum('task','approval','reminder','system') NOT NULL DEFAULT 'task' COMMENT '通知类型',
  `title` varchar(200) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读：1=是，0=否',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_node_id` (`node_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_notifications_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_notifications_node` FOREIGN KEY (`node_id`) REFERENCES `project_nodes` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- =====================================================
-- 初始化数据
-- =====================================================

-- 插入默认部门数据
INSERT INTO `departments` (`name`, `code`, `type`, `product_lines`, `manager_id`, `description`) VALUES
('销售部', 'SALES', 'collaborative', '["A","B","C","D"]', NULL, '负责项目销售和客户对接'),
('商务部', 'BUSINESS', 'collaborative', '["A","B","C","D"]', NULL, '负责商务谈判和合同管理'),
('产品A部', 'PRODUCT_A', 'main', '["A"]', NULL, '产品线A的主要执行部门'),
('产品B部', 'PRODUCT_B', 'main', '["B"]', NULL, '产品线B的主要执行部门'),
('产品C部', 'PRODUCT_C', 'main', '["C"]', NULL, '产品线C的主要执行部门'),
('产品D部门1', 'PRODUCT_D1', 'main', '["D"]', NULL, '产品线D的主要执行部门1'),
('产品D部门2', 'PRODUCT_D2', 'main', '["D"]', NULL, '产品线D的主要执行部门2'),
('数据分析部', 'DATA_ANALYSIS', 'collaborative', '["A","C","D"]', NULL, '提供数据分析支持'),
('技术支持部', 'TECH_SUPPORT', 'collaborative', '["A","B","C","D"]', NULL, '提供技术支持服务');

-- 插入工作流定义数据
INSERT INTO `workflow_definitions` (`name`, `code`, `product_line`, `description`) VALUES
('产品线A工作流', 'WORKFLOW_A', 'A', '产品线A的完整工作流程'),
('产品线B工作流', 'WORKFLOW_B', 'B', '产品线B的完整工作流程'),
('产品线C工作流', 'WORKFLOW_C', 'C', '产品线C的完整工作流程'),
('产品线D工作流', 'WORKFLOW_D', 'D', '产品线D的完整工作流程');

-- 插入系统配置数据
INSERT INTO `system_configs` (`config_key`, `config_value`, `config_type`, `config_group`, `description`, `is_public`) VALUES
('system.name', '项目流程管理系统', 'string', 'system', '系统名称', 1),
('system.version', '1.0.0', 'string', 'system', '系统版本', 1),
('email.smtp_host', 'smtp.example.com', 'string', 'email', 'SMTP服务器地址', 0),
('email.smtp_port', '587', 'number', 'email', 'SMTP端口', 0),
('email.from_address', '<EMAIL>', 'string', 'email', '发件人邮箱', 0),
('file.max_size', '10485760', 'number', 'file', '文件最大上传大小（字节）', 0),
('file.allowed_types', '["pdf","doc","docx","xls","xlsx","ppt","pptx","jpg","jpeg","png","gif"]', 'json', 'file', '允许上传的文件类型', 0),
('workflow.auto_reminder', 'true', 'boolean', 'workflow', '是否启用自动提醒', 0),
('workflow.reminder_hours', '24', 'number', 'workflow', '提醒间隔小时数', 0);

-- =====================================================
-- 索引优化建议
-- =====================================================

-- 为高频查询字段添加复合索引
ALTER TABLE `projects` ADD INDEX `idx_product_status` (`product_line`, `status`);
ALTER TABLE `projects` ADD INDEX `idx_signing_status_created` (`signing_status`, `created_at`);
ALTER TABLE `project_nodes` ADD INDEX `idx_assignee_status` (`assignee_id`, `status`);
ALTER TABLE `project_nodes` ADD INDEX `idx_dept_status` (`department_id`, `status`);
ALTER TABLE `project_logs` ADD INDEX `idx_project_action_created` (`project_id`, `action`, `created_at`);
ALTER TABLE `notifications` ADD INDEX `idx_user_read_created` (`user_id`, `is_read`, `created_at`);

-- =====================================================
-- 视图定义
-- =====================================================

-- 项目概览视图
CREATE VIEW `v_project_overview` AS
SELECT
    p.id,
    p.project_no,
    p.title,
    p.product_line,
    p.contract_amount,
    p.customer_name,
    p.signing_status,
    p.status,
    d.name as main_department_name,
    u1.real_name as sales_user_name,
    u2.real_name as business_user_name,
    pw.current_node_code,
    pw.status as workflow_status,
    p.created_at,
    p.updated_at
FROM `projects` p
LEFT JOIN `departments` d ON p.main_department_id = d.id
LEFT JOIN `users` u1 ON p.sales_user_id = u1.id
LEFT JOIN `users` u2 ON p.business_user_id = u2.id
LEFT JOIN `project_workflows` pw ON p.id = pw.project_id
WHERE p.deleted_at IS NULL;

-- 待处理任务视图
CREATE VIEW `v_pending_tasks` AS
SELECT
    pn.id,
    pn.project_workflow_id,
    p.project_no,
    p.title as project_title,
    p.product_line,
    nd.node_name,
    nd.action_required,
    pn.assignee_id,
    u.real_name as assignee_name,
    pn.department_id,
    d.name as department_name,
    pn.due_date,
    pn.created_at,
    DATEDIFF(NOW(), pn.created_at) as pending_days
FROM `project_nodes` pn
JOIN `project_workflows` pw ON pn.project_workflow_id = pw.id
JOIN `projects` p ON pw.project_id = p.id
JOIN `node_definitions` nd ON pn.node_definition_id = nd.id
LEFT JOIN `users` u ON pn.assignee_id = u.id
LEFT JOIN `departments` d ON pn.department_id = d.id
WHERE pn.status IN ('pending', 'running')
AND p.deleted_at IS NULL
AND p.status = 'active';

-- =====================================================
-- 存储过程示例
-- =====================================================

DELIMITER //

-- 创建项目工作流实例的存储过程
CREATE PROCEDURE `sp_create_project_workflow`(
    IN p_project_id BIGINT,
    IN p_product_line ENUM('A','B','C','D')
)
BEGIN
    DECLARE v_workflow_id BIGINT;
    DECLARE v_project_workflow_id BIGINT;

    -- 获取对应产品线的工作流定义
    SELECT id INTO v_workflow_id
    FROM workflow_definitions
    WHERE product_line = p_product_line AND is_active = 1
    LIMIT 1;

    IF v_workflow_id IS NOT NULL THEN
        -- 创建项目工作流实例
        INSERT INTO project_workflows (project_id, workflow_definition_id, status)
        VALUES (p_project_id, v_workflow_id, 'pending');

        SET v_project_workflow_id = LAST_INSERT_ID();

        -- 创建第一个节点实例
        INSERT INTO project_nodes (
            project_workflow_id,
            node_definition_id,
            node_code,
            status
        )
        SELECT
            v_project_workflow_id,
            id,
            node_code,
            'pending'
        FROM node_definitions
        WHERE workflow_id = v_workflow_id
        AND sequence = 1;

        -- 更新工作流状态为运行中
        UPDATE project_workflows
        SET status = 'running', started_at = NOW()
        WHERE id = v_project_workflow_id;

    END IF;
END //

DELIMITER ;

-- =====================================================
-- 数据库设计说明
-- =====================================================

/*
设计特点：

1. 模块化设计：
   - 用户管理模块：users, departments, user_departments
   - 项目管理模块：projects, project_collaborations
   - 工作流引擎模块：workflow_definitions, node_definitions
   - 项目实例模块：project_workflows, project_nodes
   - 文件管理模块：project_files
   - 日志模块：project_logs
   - 系统配置模块：system_configs
   - 通知模块：notifications

2. 可扩展性：
   - 工作流定义与实例分离，支持版本管理
   - JSON字段存储灵活数据，便于扩展
   - 枚举类型便于添加新状态
   - 软删除设计，数据安全

3. 性能优化：
   - 合理的索引设计
   - 复合索引优化高频查询
   - 视图简化复杂查询
   - 存储过程提升性能

4. 数据完整性：
   - 外键约束保证数据一致性
   - 唯一约束防止重复数据
   - 非空约束保证必要字段

5. CI4.6兼容性：
   - 遵循CI4命名规范
   - 支持软删除（deleted_at）
   - 自动时间戳（created_at, updated_at）
   - JSON字段支持

6. 业务适配：
   - 支持4条产品线差异化流程
   - 支持协同部门并行处理
   - 支持节点驳回和重置
   - 支持文件管理和操作日志
*/
