<?php

/**
 * API测试脚本
 * 测试工作流系统的API接口
 */

// 基础URL
$baseUrl = 'http://localhost:8080/api/v1';

/**
 * 发送HTTP请求
 */
function sendRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $headers[] = 'Content-Type: application/json';
    }
    
    if ($headers) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error, 'http_code' => 0];
    }
    
    return [
        'http_code' => $httpCode,
        'response' => json_decode($response, true),
        'raw_response' => $response
    ];
}

echo "=== 工作流系统API测试 ===\n\n";

// 测试项目列表API
echo "1. 测试项目列表API...\n";
$result = sendRequest($baseUrl . '/projects');
if ($result['http_code'] === 200) {
    echo "✓ 项目列表API正常\n";
    if (isset($result['response']['data'])) {
        echo "  - 返回项目数: " . count($result['response']['data']) . "\n";
    }
} else {
    echo "✗ 项目列表API失败 (HTTP {$result['http_code']})\n";
    if (isset($result['response'])) {
        echo "  - 错误: " . ($result['response']['message'] ?? 'Unknown error') . "\n";
    }
}
echo "\n";

// 测试工作流列表API
echo "2. 测试工作流列表API...\n";
$result = sendRequest($baseUrl . '/workflows');
if ($result['http_code'] === 200) {
    echo "✓ 工作流列表API正常\n";
    if (isset($result['response']['data'])) {
        echo "  - 返回工作流数: " . count($result['response']['data']) . "\n";
    }
} else {
    echo "✗ 工作流列表API失败 (HTTP {$result['http_code']})\n";
    if (isset($result['response'])) {
        echo "  - 错误: " . ($result['response']['message'] ?? 'Unknown error') . "\n";
    }
}
echo "\n";

// 测试任务列表API
echo "3. 测试任务列表API...\n";
$result = sendRequest($baseUrl . '/tasks');
if ($result['http_code'] === 200) {
    echo "✓ 任务列表API正常\n";
    if (isset($result['response']['data'])) {
        echo "  - 返回任务数: " . count($result['response']['data']) . "\n";
    }
} else {
    echo "✗ 任务列表API失败 (HTTP {$result['http_code']})\n";
    if (isset($result['response'])) {
        echo "  - 错误: " . ($result['response']['message'] ?? 'Unknown error') . "\n";
    }
}
echo "\n";

// 测试创建项目API
echo "4. 测试创建项目API...\n";
$projectData = [
    'title' => '测试项目 - ' . date('Y-m-d H:i:s'),
    'product_line' => 'A',
    'customer_name' => '测试客户',
    'customer_email' => '<EMAIL>',
    'sales_user_id' => 1,
    'description' => '这是一个API测试项目'
];

$result = sendRequest($baseUrl . '/projects', 'POST', $projectData);
if ($result['http_code'] === 201) {
    echo "✓ 创建项目API正常\n";
    $projectId = $result['response']['data']['id'] ?? null;
    if ($projectId) {
        echo "  - 新项目ID: {$projectId}\n";
        
        // 测试启动工作流
        echo "\n5. 测试启动工作流API...\n";
        $workflowResult = sendRequest($baseUrl . "/projects/{$projectId}/workflow/start", 'POST');
        if ($workflowResult['http_code'] === 200) {
            echo "✓ 启动工作流API正常\n";
        } else {
            echo "✗ 启动工作流API失败 (HTTP {$workflowResult['http_code']})\n";
            if (isset($workflowResult['response'])) {
                echo "  - 错误: " . ($workflowResult['response']['message'] ?? 'Unknown error') . "\n";
            }
        }
    }
} else {
    echo "✗ 创建项目API失败 (HTTP {$result['http_code']})\n";
    if (isset($result['response'])) {
        echo "  - 错误: " . ($result['response']['message'] ?? 'Unknown error') . "\n";
    }
}
echo "\n";

// 测试产品线工作流API
echo "6. 测试产品线工作流API...\n";
$result = sendRequest($baseUrl . '/workflows/product-line/A');
if ($result['http_code'] === 200) {
    echo "✓ 产品线工作流API正常\n";
    if (isset($result['response']['data'])) {
        echo "  - 工作流名称: " . ($result['response']['data']['name'] ?? 'N/A') . "\n";
        echo "  - 节点数量: " . count($result['response']['data']['nodes'] ?? []) . "\n";
    }
} else {
    echo "✗ 产品线工作流API失败 (HTTP {$result['http_code']})\n";
    if (isset($result['response'])) {
        echo "  - 错误: " . ($result['response']['message'] ?? 'Unknown error') . "\n";
    }
}
echo "\n";

// 测试待处理任务API
echo "7. 测试待处理任务API...\n";
$result = sendRequest($baseUrl . '/tasks/pending');
if ($result['http_code'] === 200) {
    echo "✓ 待处理任务API正常\n";
    if (isset($result['response']['data'])) {
        echo "  - 待处理任务数: " . count($result['response']['data']) . "\n";
    }
} else {
    echo "✗ 待处理任务API失败 (HTTP {$result['http_code']})\n";
    if (isset($result['response'])) {
        echo "  - 错误: " . ($result['response']['message'] ?? 'Unknown error') . "\n";
    }
}
echo "\n";

// 测试统计API
echo "8. 测试统计API...\n";
$result = sendRequest($baseUrl . '/projects/statistics');
if ($result['http_code'] === 200) {
    echo "✓ 项目统计API正常\n";
    if (isset($result['response']['data'])) {
        $stats = $result['response']['data'];
        echo "  - 总项目数: " . ($stats['total'] ?? 0) . "\n";
        echo "  - 活跃项目: " . ($stats['by_status']['active'] ?? 0) . "\n";
        echo "  - 已完成项目: " . ($stats['by_status']['completed'] ?? 0) . "\n";
    }
} else {
    echo "✗ 项目统计API失败 (HTTP {$result['http_code']})\n";
    if (isset($result['response'])) {
        echo "  - 错误: " . ($result['response']['message'] ?? 'Unknown error') . "\n";
    }
}
echo "\n";

echo "=== API测试完成 ===\n";

// 显示一些有用的信息
echo "\n=== 可用的API端点 ===\n";
echo "项目管理:\n";
echo "  GET    /api/v1/projects - 获取项目列表\n";
echo "  POST   /api/v1/projects - 创建项目\n";
echo "  GET    /api/v1/projects/{id} - 获取项目详情\n";
echo "  POST   /api/v1/projects/{id}/workflow/start - 启动工作流\n";
echo "\n";

echo "工作流管理:\n";
echo "  GET    /api/v1/workflows - 获取工作流列表\n";
echo "  GET    /api/v1/workflows/product-line/{line} - 获取产品线工作流\n";
echo "  GET    /api/v1/workflows/{id}/nodes - 获取工作流节点\n";
echo "\n";

echo "任务管理:\n";
echo "  GET    /api/v1/tasks - 获取任务列表\n";
echo "  GET    /api/v1/tasks/pending - 获取待处理任务\n";
echo "  POST   /api/v1/tasks/{id}/complete - 完成任务\n";
echo "  POST   /api/v1/tasks/{id}/reject - 驳回任务\n";
echo "\n";

echo "统计报表:\n";
echo "  GET    /api/v1/projects/statistics - 项目统计\n";
echo "  GET    /api/v1/tasks/statistics - 任务统计\n";
echo "\n";

echo "使用示例:\n";
echo "curl -X GET http://localhost:8080/api/v1/projects\n";
echo "curl -X POST http://localhost:8080/api/v1/projects -H 'Content-Type: application/json' -d '{\"title\":\"测试项目\",\"product_line\":\"A\",\"customer_name\":\"测试客户\",\"sales_user_id\":1}'\n";
