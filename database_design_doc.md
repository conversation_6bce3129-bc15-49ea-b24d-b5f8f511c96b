# 项目流程管理系统数据库设计文档

## 1. 设计理念

作为全球顶尖的web架构师，本数据库设计遵循以下核心原则：

### 1.1 模块化设计
- **高内聚低耦合**：每个模块职责明确，模块间依赖最小化
- **可插拔架构**：各模块可独立开发、测试、部署
- **清晰边界**：模块间通过标准接口交互

### 1.2 可扩展性
- **水平扩展**：支持分库分表，应对大数据量
- **垂直扩展**：JSON字段存储灵活数据，便于功能扩展
- **版本管理**：工作流定义支持版本控制，向后兼容

### 1.3 高性能
- **索引优化**：基于查询模式设计复合索引
- **查询优化**：视图简化复杂查询，存储过程提升性能
- **缓存友好**：设计支持Redis等缓存策略

### 1.4 数据安全
- **软删除**：重要数据不物理删除，支持数据恢复
- **操作审计**：完整的操作日志，支持数据追溯
- **权限控制**：基于角色的访问控制设计

## 2. 核心架构

### 2.1 分层架构
```
┌─────────────────────────────────────┐
│           应用层 (CI4.6)            │
├─────────────────────────────────────┤
│           业务逻辑层                │
├─────────────────────────────────────┤
│           数据访问层                │
├─────────────────────────────────────┤
│           数据库层 (MySQL)          │
└─────────────────────────────────────┘
```

### 2.2 模块划分
```
项目流程管理系统
├── 用户管理模块
│   ├── users (用户表)
│   ├── departments (部门表)
│   └── user_departments (用户部门关联表)
├── 项目管理模块
│   ├── projects (项目表)
│   └── project_collaborations (项目协同部门表)
├── 工作流引擎模块
│   ├── workflow_definitions (工作流定义表)
│   └── node_definitions (节点定义表)
├── 项目实例模块
│   ├── project_workflows (项目工作流实例表)
│   └── project_nodes (项目节点实例表)
├── 文件管理模块
│   └── project_files (项目文件表)
├── 日志模块
│   └── project_logs (项目操作日志表)
├── 系统配置模块
│   └── system_configs (系统配置表)
└── 通知模块
    └── notifications (通知表)
```

## 3. 核心表设计详解

### 3.1 用户管理模块

#### users (用户表)
- **设计目标**：存储系统用户基本信息
- **关键字段**：
  - `username`: 唯一用户名，支持登录
  - `email`: 唯一邮箱，支持邮件通知
  - `real_name`: 真实姓名，用于业务显示
  - `status`: 用户状态，支持禁用功能
- **扩展性**：预留avatar、phone等字段，支持用户画像扩展

#### departments (部门表)
- **设计目标**：支持复杂的部门结构和产品线关联
- **关键特性**：
  - `type`: 区分主部门和协同部门
  - `product_lines`: JSON格式存储支持的产品线，灵活配置
  - `parent_id`: 支持部门层级结构
  - `manager_id`: 部门负责人，支持工作流自动分配
- **业务适配**：完美支持4条产品线的差异化需求

#### user_departments (用户部门关联表)
- **设计目标**：支持用户多部门、多角色
- **关键特性**：
  - `role`: 用户在部门中的角色（成员、负责人、执行人员、数据分析师）
  - `is_primary`: 标识用户的主要部门
- **灵活性**：一个用户可以在多个部门担任不同角色

### 3.2 工作流引擎模块

#### workflow_definitions (工作流定义表)
- **设计目标**：定义可复用的工作流模板
- **关键特性**：
  - `product_line`: 每条产品线对应独立的工作流
  - `version`: 支持工作流版本管理
  - `is_active`: 支持工作流的启用/禁用
- **扩展性**：支持工作流的迭代升级，向后兼容

#### node_definitions (节点定义表)
- **设计目标**：定义工作流中的节点模板
- **关键特性**：
  - `node_type`: 区分主节点和协同节点
  - `assignee_type`: 定义节点处理人类型，支持自动分配
  - `action_required`: JSON格式存储节点操作要求
  - `can_reject`: 支持节点驳回功能
  - `reject_to_node`: 定义驳回目标节点
  - `is_parallel`: 支持并行节点（协同部门）
- **业务适配**：完美支持复杂的业务流程需求

### 3.3 项目实例模块

#### project_workflows (项目工作流实例表)
- **设计目标**：管理具体项目的工作流执行
- **关键特性**：
  - `current_node_code`: 当前执行节点
  - `status`: 工作流执行状态
  - 时间字段：支持工作流执行时间统计
- **性能优化**：通过current_node_code快速定位当前状态

#### project_nodes (项目节点实例表)
- **设计目标**：管理具体项目的节点执行
- **关键特性**：
  - `assignee_id`: 节点处理人
  - `department_id`: 协同部门（协同节点使用）
  - `result_data`: JSON格式存储节点处理结果
  - `reject_reason`: 驳回原因
- **灵活性**：支持主节点和协同节点的差异化处理

## 4. 业务流程映射

### 4.1 产品线A流程映射
```
主节点1(销售创建) → 主节点2(选择执行人员) → 主节点3(上传方案) 
→ 主节点4(审批方案) → 主节点5(更新产品信息) 
→ [分支：未中标 → 主节点6(商务审核) → 结束]
→ [分支：中标 → 主节点6(选择协同部门)]
→ [协同流程：协同节点1-4 并行执行]
→ 主节点7(确认数据&上传PPT) → 主节点8(审批PPT&交付) → 结束
```

### 4.2 协同流程设计
- **并行执行**：多个协同部门可同时执行
- **独立状态**：每个协同部门有独立的节点状态
- **回归主流程**：协同完成后统一回到主流程

## 5. 性能优化策略

### 5.1 索引设计
```sql
-- 高频查询优化
ALTER TABLE `projects` ADD INDEX `idx_product_status` (`product_line`, `status`);
ALTER TABLE `project_nodes` ADD INDEX `idx_assignee_status` (`assignee_id`, `status`);
```

### 5.2 查询优化
- **视图简化**：`v_project_overview`、`v_pending_tasks`
- **存储过程**：`sp_create_project_workflow`
- **分页优化**：基于主键的分页策略

### 5.3 缓存策略
- **工作流定义缓存**：减少重复查询
- **用户权限缓存**：提升权限验证性能
- **部门结构缓存**：优化组织架构查询

## 6. CI4.6框架适配

### 6.1 命名规范
- **表名**：小写+下划线，符合CI4规范
- **字段名**：小写+下划线，语义明确
- **主键**：统一使用`id`，bigint类型

### 6.2 框架特性支持
- **软删除**：`deleted_at`字段，配合CI4软删除功能
- **时间戳**：`created_at`、`updated_at`自动维护
- **JSON字段**：利用CI4的JSON字段支持
- **模型关联**：外键设计支持Eloquent关联

### 6.3 扩展性考虑
- **中间件支持**：权限验证、操作日志
- **事件系统**：工作流状态变更事件
- **队列支持**：异步通知、邮件发送

## 7. 安全性设计

### 7.1 数据安全
- **软删除**：重要数据不物理删除
- **操作审计**：完整的操作日志记录
- **数据加密**：敏感字段加密存储

### 7.2 访问控制
- **角色权限**：基于部门角色的权限控制
- **数据隔离**：用户只能访问授权的项目数据
- **操作权限**：节点操作权限精确控制

## 8. 监控与运维

### 8.1 性能监控
- **慢查询监控**：识别性能瓶颈
- **索引使用率**：优化索引策略
- **连接池监控**：数据库连接管理

### 8.2 数据备份
- **定期备份**：自动化数据备份策略
- **增量备份**：减少备份时间和存储空间
- **恢复测试**：定期验证备份有效性

这套数据库设计充分考虑了业务复杂性、系统可扩展性和框架兼容性，为项目流程管理系统提供了坚实的数据基础。
