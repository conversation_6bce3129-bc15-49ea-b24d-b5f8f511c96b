<?php

declare(strict_types=1);

/*
 * The MIT License (MIT)
 *
 * Copyright (c) 2013 <PERSON> (<EMAIL>), <PERSON><PERSON><PERSON> (<EMAIL>)
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

namespace Kint\Parser;

use Kint\Value\AbstractValue;
use Kint\Value\Context\BaseContext;
use Kint\Value\EnumValue;
use Kint\Value\Representation\ContainerRepresentation;
use UnitEnum;

class EnumPlugin extends AbstractPlugin implements PluginCompleteInterface
{
    private array $cache = [];

    public function getTypes(): array
    {
        return ['object'];
    }

    public function getTriggers(): int
    {
        if (!KINT_PHP81) {
            return Parser::TRIGGER_NONE;
        }

        return Parser::TRIGGER_SUCCESS;
    }

    public function parseComplete(&$var, AbstractValue $v, int $trigger): AbstractValue
    {
        if (!$var instanceof UnitEnum) {
            return $v;
        }

        $c = $v->getContext();
        $class = \get_class($var);

        if (!isset($this->cache[$class])) {
            $contents = [];

            foreach ($var->cases() as $case) {
                $base = new BaseContext($case->name);
                $base->access_path = '\\'.$class.'::'.$case->name;
                $base->depth = $c->getDepth() + 1;
                $contents[] = new EnumValue($base, $case);
            }

            /** @psalm-var non-empty-array<EnumValue> $contents */
            $this->cache[$class] = new ContainerRepresentation('Enum values', $contents, 'enum');
        }

        $object = new EnumValue($c, $var);
        $object->flags = $v->flags;
        $object->appendRepresentations($v->getRepresentations());
        $object->addRepresentation($this->cache[$class], 0);

        return $object;
    }
}
