<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Time language settings
return [
    'invalidFormat'  => '"{0}" is not a valid datetime format',
    'invalidMonth'   => 'Months must be between 1 and 12. Given: {0}',
    'invalidDay'     => 'Days must be between 1 and 31. Given: {0}',
    'invalidOverDay' => 'Days must be between 1 and {0}. Given: {1}',
    'invalidHours'   => 'Hours must be between 0 and 23. Given: {0}',
    'invalidMinutes' => 'Minutes must be between 0 and 59. Given: {0}',
    'invalidSeconds' => 'Seconds must be between 0 and 59. Given: {0}',
    'years'          => '{0, plural, =1{# year} other{# years}}',
    'months'         => '{0, plural, =1{# month} other{# months}}',
    'weeks'          => '{0, plural, =1{# week} other{# weeks}}',
    'days'           => '{0, plural, =1{# day} other{# days}}',
    'hours'          => '{0, plural, =1{# hour} other{# hours}}',
    'minutes'        => '{0, plural, =1{# minute} other{# minutes}}',
    'seconds'        => '{0, plural, =1{# second} other{# seconds}}',
    'ago'            => '{0} ago',
    'inFuture'       => 'in {0}',
    'yesterday'      => 'Yesterday',
    'tomorrow'       => 'Tomorrow',
    'now'            => 'Just now',
];
